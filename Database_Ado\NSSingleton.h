#pragma once

#include <mutex>

template <typename T>
class TemplateSingleton
{
protected:
	TemplateSingleton()
	{
	}
	virtual ~TemplateSingleton()
	{
	}

public:
	static T* GetInstance()
	{
		if (m_pInstance == nullptr)
		{
			std::lock_guard<std::mutex> lock(m_mutexlock);

			if (m_pInstance == nullptr)
			{
				m_pInstance = new T;
			}
		}
		return m_pInstance;
	};

	static void DestroyInstance()
	{
		if (m_pInstance)
		{
			delete m_pInstance;
			m_pInstance = nullptr;
		}
	};

private:
	static T*		  m_pInstance;
	static std::mutex m_mutexlock;
};

template <typename T>
T* TemplateSingleton<T>::m_pInstance = 0;

template <typename T>
std::mutex TemplateSingleton<T>::m_mutexlock;