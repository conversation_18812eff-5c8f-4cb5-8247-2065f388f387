#pragma once
#include <memory>
#include <functional>
#include <mysql.h>

namespace Database
{
    // MySQL 리소스를 위한 RAII 래퍼
    template<typename T>
    using mysql_unique_ptr = std::unique_ptr<T, std::function<void(T*)>>;

    // MYSQL_RES를 위한 RAII 래퍼
    inline mysql_unique_ptr<MYSQL_RES> make_mysql_result(MYSQL_RES* res)
    {
        return mysql_unique_ptr<MYSQL_RES>(res, [](MYSQL_RES* r) {
            if (r) mysql_free_result(r);
        });
    }

    // MYSQL_STMT를 위한 RAII 래퍼
    inline mysql_unique_ptr<MYSQL_STMT> make_mysql_stmt(MYSQL_STMT* stmt)
    {
        return mysql_unique_ptr<MYSQL_STMT>(stmt, [](MYSQL_STMT* s) {
            if (s) mysql_stmt_close(s);
        });
    }

    // MYSQL을 위한 RAII 래퍼
    inline mysql_unique_ptr<MYSQL> make_mysql_connection(MYSQL* conn)
    {
        return mysql_unique_ptr<MYSQL>(conn, [](MYSQL* c) {
            if (c) mysql_close(c);
        });
    }

    // 자동 리소스 정리를 위한 스코프 가드
    template<typename F>
    class ScopeGuard
    {
    public:
        explicit ScopeGuard(F&& f) : m_func(std::move(f)), m_active(true) {}
        ~ScopeGuard() { if (m_active) m_func(); }
        
        ScopeGuard(ScopeGuard&& other) noexcept
            : m_func(std::move(other.m_func)), m_active(other.m_active)
        {
            other.m_active = false;
        }
        
        void dismiss() { m_active = false; }
        
    private:
        F m_func;
        bool m_active;
    };

    template<typename F>
    ScopeGuard<F> make_scope_guard(F&& f)
    {
        return ScopeGuard<F>(std::forward<F>(f));
    }

    // MySQL 에러 체크 헬퍼
    inline void check_mysql_error(MYSQL* mysql, const char* operation)
    {
        if (mysql_errno(mysql) != 0)
        {
            throw std::runtime_error(std::string(operation) + " failed: " + mysql_error(mysql));
        }
    }

    inline void check_stmt_error(MYSQL_STMT* stmt, const char* operation)
    {
        if (mysql_stmt_errno(stmt) != 0)
        {
            throw std::runtime_error(std::string(operation) + " failed: " + mysql_stmt_error(stmt));
        }
    }
}