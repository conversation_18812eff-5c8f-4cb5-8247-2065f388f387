#pragma once
#include "../mimalloc_integration.h"
#include "../db_concurrentqueue.h"
#include <thread>
#include <functional>
#include <atomic>
#include <chrono>

namespace Database
{
    // 워커 스레드 풀 관리자 (내부 컴포넌트)
    class WorkerThreadPool
    {
    public:
        WorkerThreadPool() = default;
        ~WorkerThreadPool();
        
        // 워커 스레드 시작
        bool Start(uint32_t threadCount);
        
        // 워커 스레드 중지
        void Stop();
        
        // 작업 추가
        void PostWork(std::function<void()> work);
        
        // 현재 활성 스레드 수
        uint32_t GetThreadCount() const { return static_cast<uint32_t>(m_workerThreads.size()); }
        
        // 대기 중인 작업 수
        size_t GetPendingWorkCount() const;
        
        // 실행 중 여부
        bool IsRunning() const { return m_running.load(); }
        
    private:
        // 워커 스레드 함수
        void WorkerThreadFunc();
        
        // 워커 스레드들
        mi::vector<std::thread> m_workerThreads;  // mimalloc 사용
        
        // 작업 큐 (lock-free blocking queue)
        Database::BlockingConcurrentQueue<std::function<void()>> m_workQueue;
        
        // 실행 상태
        std::atomic<bool> m_running{false};
        std::atomic<bool> m_stopRequested{false};
    };
}