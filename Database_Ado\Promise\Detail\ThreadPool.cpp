#include "stdafx.h"
#include "ThreadPool.h"

#include "Promise/Detail/Utility.h"

namespace Promise::Detail
{
	ThreadPool::ThreadPool()
		: m_WorkGuard(asio::make_work_guard(m_IoContext))
	{
		std::vector<std::future<std::thread::id>> workerIds;

		size_t concurrency = std::thread::hardware_concurrency();
		for (size_t i = 0; i < concurrency; ++i)
		{
			std::promise<std::thread::id> promise;
			workerIds.push_back(promise.get_future());

			m_Workers.emplace_back(Wrap([this, promise = std::move(promise)]() mutable
				{
					std::thread::id tid = std::this_thread::get_id();

					SetCurrentExecutor(tid, this);
					promise.set_value(tid);

					m_IoContext.run();

					SetCurrentExecutor(tid, nullptr);
				}));
		}

		std::ranges::for_each(workerIds, [this](std::future<std::thread::id>& future)
			{
				(void)m_WorkerIds.emplace(future.get());
			});

		for (size_t i = 0; i < m_Workers.size(); ++i)
		{
			m_Workers[i].SetDescription(std::format("NSThreadPoolWorker#{}", i));
		}
	}

	ThreadPool::~ThreadPool()
	{
		m_IoContext.stop();

		for (NSThread& worker : m_Workers)
		{
			worker.Wait();
		}
	}

	bool ThreadPool::RunningInThisThread() const
	{
		return m_WorkerIds.contains(std::this_thread::get_id());
	}

	void ThreadPool::Post(const std::function<void()>& function)
	{
		m_IoContext.post(function);
	}

	void ThreadPool::Delay(const std::function<void()>& function, uint64_t milliseconds)
	{
		auto timer = std::make_shared<asio::steady_timer>(m_IoContext);
		timer->expires_after(std::chrono::milliseconds(milliseconds));
		timer->async_wait([function, this, timer](const std::error_code& ec)
			{
				if (!ec)
				{
					this->Post(function);
				}
			});
	}

	auto ThreadPool::GetInstance() -> ThreadPool&
	{
		static ThreadPool promiseExecutor;
		return promiseExecutor;
	}
}
