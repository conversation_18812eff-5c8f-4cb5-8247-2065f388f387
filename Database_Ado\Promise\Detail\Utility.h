#pragma once
#include <type_traits>
#include <memory>

namespace Promise::Detail
{
	template <class Callable>
	auto Wrap(Callable&& callable)
	{
		if constexpr (!std::is_copy_assignable_v<Callable>)
		{
			return [pf = std::make_shared<std::decay_t<Callable>>(std::forward<Callable>(callable))] <typename... Args>(Args&&...args) -> decltype(auto)
			{
				return (*pf)(Args(std::forward<Args>(args))...);
			};
		}
		else
		{
			return callable;
		}
	}
}

#define WRAP_MOVE_ONLY_CALLABLE(callable) (Promise::Detail::Wrap(std::forward<Callable>(callable)))