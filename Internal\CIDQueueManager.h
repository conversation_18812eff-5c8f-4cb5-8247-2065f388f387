#pragma once
#include "../mimalloc_integration.h"
#include "../NSDefine.h"
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <queue>

namespace Database
{
    struct QueryTask;
    
    // CID별 작업 큐 관리자 (내부 컴포넌트)
    class CIDQueueManager
    {
    public:
        CIDQueueManager() = default;
        ~CIDQueueManager() = default;
        
        // CID별로 작업 추가
        bool EnqueueTask(int64_t cid, QueryTask task);
        
        // CID의 다음 작업 가져오기
        bool DequeueTask(int64_t cid, QueryTask& task);
        
        // CID에 대기 중인 작업이 있는지 확인
        bool HasPendingTasks(int64_t cid) const;
        
        // CID의 모든 작업 제거
        void ClearCIDTasks(int64_t cid);
        
        // 전체 큐 크기
        size_t GetTotalQueueSize() const;
        
    private:
        // CID별 작업 큐
        struct CIDQueue
        {
            mi::queue<QueryTask> tasks;  // mimalloc 사용
            mutable std::mutex mutex;    // CID별 개별 락
            std::chrono::steady_clock::time_point lastAccessTime; // 마지막 접근 시간
            
            void UpdateAccessTime() 
            {
                std::lock_guard<std::mutex> lock(mutex);
                lastAccessTime = std::chrono::steady_clock::now();
            }
            
            bool IsInactive(std::chrono::seconds timeout) const
            {
                std::lock_guard<std::mutex> lock(mutex);
                return tasks.empty() && 
                       (std::chrono::steady_clock::now() - lastAccessTime) > timeout;
            }
        };
        
        // CID -> 큐 매핑
        mi::unordered_map<int64_t, std::shared_ptr<CIDQueue>> m_cidQueues;  // mimalloc 사용
        mutable std::shared_mutex m_cidQueuesMutex;
        
        // CID별 큐 가져오기 (없으면 생성)
        std::shared_ptr<CIDQueue> GetOrCreateQueue(int64_t cid);
    };
}