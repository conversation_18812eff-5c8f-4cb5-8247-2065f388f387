#pragma once

#include "NSAdoCommand.h"

class NSAdoMySQLCommand : public NSAdoCommand
{
public:
	NSAdoMySQLCommand();
	virtual ~NSAdoMySQLCommand();

public :
	bool Execute(NSPQueryData* pcQueryData) override;

	bool SetItem(const char* pFieldName, const char* pValue) override;
	bool SetItem(const char* pFieldName, const bool bValue) override;
	bool SetItem(const char* pFieldName, const uint8_t uValue) override;
	bool SetItem(const char* pFieldName, const uint16_t uValue) override;
	bool SetItem(const char* pFieldName, const uint32_t uValue) override;
	bool SetItem(const char* pFieldName, const uint32_t uIndex, const uint16_t uValue) override;
	bool SetItem(const char* pFieldName, const uint32_t uIndex, const int iValue) override;
	bool SetItem(const char* pFieldName, const uint32_t uIndex, const uint32_t uValue) override;
	bool SetItem(const char* pFieldName, const int iValue) override;
	bool SetItem(const char* pFieldName, const int64_t iValue) override;
	bool SetItem(const char* pFieldName, const uint64_t uValue) override;
	bool SetItem(const char* pFieldName, const float fValue) override;
	bool SetItem(const char* pFieldName, const uint32_t uIndex, const uint64_t uValue) override;
	bool SetItem(const char* pFieldName, _variant_t& value) override;
	bool SetItem(const char* pFieldName, SYSTEMTIME& dtTime) override;
	bool SetItem(const char* pFieldName, NPDateTime& dtTime) override;
	bool SetItemBinary(const char* pFieldName, byte* pValue, int iSize) override;

	//GET
	bool GetItem(const char* pFieldName, bool& bValue) override;
	bool GetItem(const char* pFieldName, uint16_t& iValue) override;
	bool GetItem(const char* pFieldName, uint32_t& iValue) override;
	bool GetItem(const char* pFieldName, uint64_t& iValue) override;
	bool GetItem(const char* pFieldName, const uint32_t uIndex, uint8_t& iValue) override;
	bool GetItem(const char* pFieldName, const uint32_t uIndex, uint16_t& iValue) override;
	bool GetItem(const char* pFieldName, const uint32_t uIndex, int32_t& iValue) override;
	bool GetItem(const char* pFieldName, const uint32_t uIndex, uint32_t& iValue) override;
	bool GetItem(const char* pFieldName, const uint32_t uIndex, uint64_t& iValue) override;
	bool GetItem(const char* pFieldName, uint8_t& iValue) override;
	bool GetItem(const char* pFieldName, int& iValue) override;
	bool GetItem(const char* pFieldName, int64_t& liValue) override;
	bool GetItem(const char* pFieldName, float& fValue) override;
	bool GetItem(const char* pFieldName, NPDateTime& dtTime) override;
	bool GetItem(const char* lpszField, int8_t& iValue) override;
	bool GetItem(const char* pFieldName, char* pValue, int iSize) override;
	bool GetItemUTF8(const char* pFieldName, char* pValue, int iSize) override;
	bool GetItemBinary(char* pData, byte* pValue, int iSize, const char* pFieldName) override;
};
