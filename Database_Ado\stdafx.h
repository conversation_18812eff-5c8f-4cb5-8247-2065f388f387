#pragma once
#include <cstdint>
#include <cassert>

#include <string>
#include <sstream>
#include <fstream>
#include <iostream>

#include <memory>
#include <chrono>
#include <utility>
#include <algorithm>
#include <numeric>
#include <limits>
#include <system_error>

#include <array>
#include <vector>
#include <list>
#include <forward_list>
#include <deque>
#include <map>
#include <set>
#include <unordered_map>
#include <unordered_set>
#include <stack>
#include <queue>
#include <random>
#include <regex>

#include <atomic>
#include <thread>
#include <mutex>
#include <shared_mutex>
#include <condition_variable>
#include <future>
#include <concurrent_queue.h>

#include <filesystem>
#include <optional>
#include <functional>
#include <variant>
#include <any>

namespace fs = std::filesystem;

#include <concepts>
#include <coroutine>
#include <source_location>
#include <ranges>

namespace sr = std::ranges;
namespace srv = sr::views;

#include <NSLogDefine.h>
#include "json11.hpp"

#pragma warning(push)
#pragma warning(disable:26495 26800 26819 28020)
#include <nlohmann/json.hpp>
#pragma warning(pop)

#pragma warning(push)
#pragma warning(disable:4996 5054 26495 26800)
#define RAPIDJSON_NO_SIZETYPEDEFINE
namespace rapidjson { typedef ::std::size_t SizeType; }

#include <rapidjson/document.h>
#include <rapidjson/writer.h>
#include <rapidjson/stringbuffer.h>
#pragma warning(pop)

#define CURL_STATICLIB
#include <curl/curl.h>

#include "NPErrorCode.h"
#include "NSDefine.h"
#include "NSLogDefine.h"
#include "NPDefineMacro.h"

#include "Curl/NSCurl.h"
#include "NSUtil/NSUtil.h"
#include "NSUtil/NSUtilString.h"
#include "NSUtil/NSConvertUtil.h"

#include "NPUtil.h"

#include "NSTickManager/NSTickManager.h"
