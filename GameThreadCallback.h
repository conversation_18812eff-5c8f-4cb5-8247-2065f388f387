#pragma once
#include <functional>
#include <atomic>

// 게임 스레드로 작업을 전달하는 간단한 콜백 클래스
class GameThreadCallback
{
public:
    // 게임 스레드로 작업 전달
    static void PostToGameThread(void* executor, std::function<void()> task)
    {
        if (s_dispatcher)
        {
            s_dispatcher(std::move(task));
        }
        else
        {
            // 디스패처가 설정되지 않은 경우 직접 실행 (테스트용)
            task();
        }
    }
    
    // 게임 서버에서 디스패처 설정
    static void SetGameThreadDispatcher(std::function<void(std::function<void()>)> dispatcher)
    {
        s_dispatcher = std::move(dispatcher);
    }
    
private:
    static inline std::function<void(std::function<void()>)> s_dispatcher;
};