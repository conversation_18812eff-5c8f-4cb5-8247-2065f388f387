#pragma once
#include "NPModels.h"
#include "NSDefineEnum.h"

#define DECLARE_PROCEDURE_UTIL \
protected: \
	const EDataBase GetProcedureHost() const { return procedureHost; } \
	const char* GetProcedureName() const { return procedureName; } \
public: \
	static const char* GetName() { return procedureName; }

struct NSStorageUpdateContainer;
class NSQueryData;
class NSAdoCommand;
class NSStoredProcedure
{
public:
	EErrorCode QueryFunc(const std::shared_ptr<NSQueryData> queryData);
	EErrorCode StorageQueryFunc(const std::shared_ptr<NSQueryData> queryData, const std::shared_ptr<NSStorageUpdateContainer> container);
	bool IsValid() const { return m_ReturnValue == 0; }

protected:
	virtual const char* GetProcedureName() const = 0;
	virtual const EDataBase GetProcedureHost() const = 0;
	virtual EErrorCode MakeQuery(NSAdoCommand* command) = 0;
	virtual EErrorCode MakeStorageQuery(NSAdoCommand*, NSStorageUpdateContainer*) {
		return EErrorCode::DBArgumentError;
	}
	virtual EErrorCode MakeOutput(NSAdoCommand*) {
		return EErrorCode::None;
	}
	virtual EErrorCode HandleReturnValue(const int32_t value) {
		return value < 100
			? EErrorCode::DBError
			: static_cast<EErrorCode>(value);
	}

protected:
	int32_t m_ReturnValue = -1;
};

struct NSInput_HasReward
{
	static constexpr const char* PARAMETER_REWARD_INPUT = "RewardPayload";
	//static constexpr const char* PARAMETER_INVENTORY_INPUT = "@MaxBagSize";
	static constexpr int MAX_REWARD_PAYLOAD_SIZE = 8000;
	static constexpr int MAX_REWARD_PAYLOAD_OVERFLOW_FLAG = 0x00D0BA00;

	//int32_t MaxBagSize = -1;
	char RewardPayload[MAX_REWARD_PAYLOAD_SIZE + 1] = {};

	bool IsVaildRewardInput() const;
	void AssignRewardInput(const char* payload);
	EErrorCode SetRewardInput(const int64_t aid, const int64_t cid, NSAdoCommand* command, const char* payloadField = PARAMETER_REWARD_INPUT);
};

struct NSOutput_HasReward
{
	static constexpr const char* PARAMETER_TIMESTAMP_OUTPUT = "ResultTimestamp";
	static constexpr const char* PARAMETER_REWARD_OUTPUT = "ResultReward";
	static constexpr int MAX_REWARD_OUTPUT_SIZE = 4000;

	int64_t ResultTimestamp = -1;
	char ResultReward[MAX_REWARD_OUTPUT_SIZE + 1] = {};

	void GetRewardOutput(NSAdoCommand* command);
};

struct NSInput_HasConsume
{
	static constexpr const char* PARAMETER_CONSUME_INPUT = "ConsumePayload";
	static constexpr int MAX_CONSUME_PAYLOAD_SIZE = g_iMaxConsumePayload;

	char ConsumePayload[MAX_CONSUME_PAYLOAD_SIZE + 1] = {};

	void AssignConsumeInput(const char* payload);
	EErrorCode SetConsumeInput(const int64_t aid, const int64_t cid, NSAdoCommand* command);
};

struct NSOutput_HasConsume
{
	static constexpr const char* PARAMETER_TIMESTAMP_OUTPUT = "ResultTimestamp";
	static constexpr const char* PARAMETER_CONSUME_OUTPUT = "ResultConsume";
	static constexpr int MAX_CONSUME_OUTPUT_SIZE = 4000;

	int64_t ResultTimestamp = -1;
	char ResultConsume[MAX_CONSUME_OUTPUT_SIZE + 1] = {};

	void GetConsumeOutput(NSAdoCommand* command);
};

struct NSInput_HasConsumeReward : NSInput_HasConsume, NSInput_HasReward
{
	void AssignInputs(const char* consume, const char* reward);
	EErrorCode SetInputs(const int64_t aid, const int64_t cid, NSAdoCommand* command);
};

struct NSOutput_HasConsumeReward : NSOutput_HasConsume, NSOutput_HasReward
{
	using NSOutput_HasConsume::PARAMETER_TIMESTAMP_OUTPUT;
	using NSOutput_HasConsume::ResultTimestamp;
	void GetOutputs(NSAdoCommand* command);
};

struct NSInput_CoolTime
{
	int32_t  CoolTimeContentsType = 0;
	int64_t  SubId = 0;
	uint64_t ExpiredTime = 0;

	void Set(const int32_t coolTimeContentsType, const int64_t subId, const int64_t expiredTime)
	{
		CoolTimeContentsType = coolTimeContentsType;
		SubId = subId;
		ExpiredTime = expiredTime;
	}
};
