# 규칙 2번 게임 스레드 복귀 - 안전한 수정 방안

## 수정 원칙
- 기존 테스트 코드 호환성 유지
- 프로덕션 안전성 강제
- 명확한 에러 메시지 제공
- 점진적 마이그레이션 지원

## 방안 1: 테스트 모드 플래그 도입 (권장)

### GameThreadCallback.h 수정
```cpp
#pragma once
#include <functional>
#include <atomic>
#include <stdexcept>

class GameThreadCallback
{
public:
    // 테스트 모드 설정 (기본값: false = 프로덕션 모드)
    static void SetTestMode(bool testMode) {
        s_testMode = testMode;
    }
    
    static bool IsTestMode() {
        return s_testMode;
    }
    
    // 게임 스레드로 작업 전달
    static void PostToGameThread(void* executor, std::function<void()> task)
    {
        if (s_dispatcher)
        {
            s_dispatcher(std::move(task));
        }
        else
        {
            if (s_testMode) {
                // 테스트 모드에서만 직접 실행 허용
                task();
            } else {
                // 프로덕션 모드에서는 예외 발생
                throw std::runtime_error(
                    "CRITICAL: Game thread dispatcher not set! "
                    "Call SetGameThreadDispatcher() before using DB operations. "
                    "For testing, use SetTestMode(true)."
                );
            }
        }
    }
    
    // 게임 서버에서 디스패처 설정
    static void SetGameThreadDispatcher(std::function<void(std::function<void()>)> dispatcher)
    {
        s_dispatcher = std::move(dispatcher);
    }
    
private:
    static inline std::function<void(std::function<void()>)> s_dispatcher;
    static inline bool s_testMode = false;  // 기본값: 프로덕션 모드
};
```

### NSDataBaseManager 수정
```cpp
// Start 메서드에 검증 추가
bool NSDataBaseManager::Start(uint32_t workThreadCnt)
{
    if (!m_initialized)
        return false;
    
    // 프로덕션 모드에서 디스패처 검증
    if (!GameThreadCallback::IsTestMode() && !m_gameThreadPost) {
        LOGE << "CRITICAL: Game thread dispatcher not set in production mode! "
             << "Call SetGameThreadDispatcher() before Start() or use SetTestMode(true) for testing.";
        return false;
    }
    
    // ... 기존 코드 ...
}

// 쿼리 실행 콜백 처리 부분
if (gameThreadPost && instance && !instance->m_isShuttingDown.load())
{
    gameThreadPost([task]() mutable {
        task.promise.SetValue(task.queryData);
    });
}
else
{
    if (GameThreadCallback::IsTestMode()) {
        // 테스트 모드에서만 워커 스레드 실행 허용
        task.promise.SetValue(task.queryData);
    } else {
        // 프로덕션에서는 에러 설정
        task.queryData->SetError("Game thread dispatcher not available");
        task.promise.SetError(std::runtime_error("Game thread dispatcher not set"));
    }
}
```

## 방안 2: 컴파일 타임 분리 (대안)

```cpp
#ifdef DATABASE_MARIA_TEST_MODE
    // 테스트 빌드에서만 직접 실행 허용
    task();
#else
    // 프로덕션 빌드에서는 예외
    throw std::runtime_error("Game thread dispatcher not set!");
#endif
```

## 마이그레이션 가이드

### 1단계: 경고 모드 (1주)
```cpp
if (!s_dispatcher && !s_testMode) {
    LOGW << "WARNING: Using worker thread for callback. "
         << "This will be an error in the next version!";
}
task();  // 아직은 실행
```

### 2단계: 테스트 모드 필수 (2주차)
```cpp
if (!s_dispatcher && !s_testMode) {
    throw std::runtime_error("Set test mode or dispatcher!");
}
```

### 3단계: 프로덕션 강제 (3주차)
- 최종 코드 적용

## 사용 예시

### 게임 서버 (프로덕션)
```cpp
int main() {
    auto* dbManager = NSDataBaseManager::GetInstance();
    
    // 반드시 디스패처 설정
    dbManager->SetGameThreadDispatcher([](auto task) {
        GameThreadScheduler::Post(std::move(task));
    });
    
    dbManager->Initialize();
    dbManager->Start();  // 디스패처 없으면 실패
}
```

### 테스트 코드
```cpp
TEST(DatabaseTest, QueryExecution) {
    // 테스트 모드 활성화
    GameThreadCallback::SetTestMode(true);
    
    auto* dbManager = NSDataBaseManager::GetInstance();
    dbManager->Initialize();
    dbManager->Start();  // 테스트 모드에서는 성공
    
    // 테스트 실행...
}
```

## 장점
1. **기존 테스트 코드 호환**: SetTestMode(true)만 추가하면 됨
2. **프로덕션 안전성**: 실수로 디스패처 누락 시 즉시 실패
3. **명확한 에러 메시지**: 문제 원인과 해결 방법 제시
4. **점진적 적용 가능**: 경고 → 테스트 모드 → 강제 순으로 적용

## 영향 범위
- **기존 게임 서버**: SetGameThreadDispatcher 이미 호출하면 영향 없음
- **테스트 코드**: SetTestMode(true) 추가 필요
- **신규 프로젝트**: 처음부터 안전하게 시작 가능