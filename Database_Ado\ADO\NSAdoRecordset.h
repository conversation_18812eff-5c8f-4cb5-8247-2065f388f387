#pragma once

#include "NSAdoDefine.h"
#include "NSDefine.h"

struct NPDateTime;
class NSAdoConnection;
class NSAdoRecordset
{
public:
	NSAdoRecordset();
	virtual ~NSAdoRecordset();

	bool Open(NSAdoConnection* pcConnection, const char* query);

	bool IsEOF();
	void Close();
	void Reset();

	void dump_com_error(const _com_error& e, const char* commadText, const char* pFieldName);

	//GET
	bool GetItem(const char* pFieldName, bool& bValue);
	bool GetItem(const char* pFieldName, uint8_t& uValue);
	bool GetItem(const char* pFieldName, uint16_t& uValue);
	bool GetItem(const char* pFieldName, uint32_t& uValue);
	bool GetItem(const char* pFieldName, int64_t& iValue);
	bool GetItem(const char* pFieldName, uint64_t& uValue);
	bool GetItem(const char* pFieldName, int& iValue);
	bool GetItem(const char* pFieldName, int16_t& iValue);
	bool GetItem(const char* pFieldName, float& fValue);
	bool GetItem(const char* pFieldName, char& value);
	bool GetItem(const char* pFieldName, char* value, int iSize);
	bool GetItemBinary(char* pFieldName, void* pValue, int& iSize);
	bool GetItem(const char* pFieldName, NPDateTime& dtValue);
	bool GetItem(const char* pFieldName, uint32_t uIndex, uint16_t& uValue);
	bool GetItem(const char* pFieldName, uint32_t uIndex, int& iValue);
	bool GetItem(const char* pFieldName, uint32_t uIndex, uint32_t& uValue);
	bool GetItem(const char* pFieldName, uint32_t uIndex, uint64_t& uValue);
	bool GetItem(const char* pFieldName, uint32_t uIndex, uint8_t& uValue);
	template<size_t length>
	bool GetItem(const char* fieldName, char(&value)[length])
	{
		return GetItem(fieldName, value, length);
	}
	bool GetItem(const char* fieldName, std::string& value);

	template<typename T>
	typename std::enable_if<std::is_enum<T>::value, bool>::type
		GetItem(const char* fieldName, T& value)
	{
		int intValue = 0;
		if (!GetItem(fieldName, intValue))
			return false;

		value = static_cast<T>(intValue);
		return true;
	}

	void SetRecordset(_RecordsetPtr pRecordSet);

	_RecordsetPtr GetRecordset() { return m_pRecordset; }

	const char* GetCommandName() { return m_szCommandName.c_str(); }
	void SetCommandName(const char* pCommandName) { m_szCommandName = pCommandName; }

private:
	unsigned long GetStringHash(const char* pcStr);
	void LoadColumn(_RecordsetPtr m_pRecordset);
	int GetColumnIndex(const char* pColumnName);
	ADODB::FieldPtr GetColumn(const char* pColumnName, const DataTypeEnum eType);

private:
	static constexpr unsigned char DECIMAL_SIGN_NEGATIVE = 0x80;

private:
	_RecordsetPtr m_pRecordset;
	bool		  m_bFirstRecord;

	std::string m_szCommandName;

	//유니코드 버퍼
	WCHAR m_unicode[ADORECORDSET_UNICODE_BUFFER_SIZE] = { 0 };
	std::unordered_map<long, int> m_mapColumnIndex;
};
