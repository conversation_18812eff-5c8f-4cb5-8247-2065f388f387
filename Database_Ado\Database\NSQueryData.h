#pragma once

#include "QueryData/NSPQueryData.h"

class NSQueryData;
class NSDBSession;
using TYPE_QUERY_FUNC = std::function<EErrorCode(std::shared_ptr<NSQueryData>)>;
using TYPE_RESULT_FUNC = std::function<void(std::shared_ptr<NSQueryData>)>;

#define QUERY_FUNC(TARGET, QUERYFUNC) std::bind(QUERYFUNC, TARGET, std::placeholders::_1)
#define RESULT_FUNC(TARGET, RESULTFUNC) std::bind(RESULTFUNC, TARGET, std::placeholders::_1)

class NSQueryData : public NSPQueryData, public std::enable_shared_from_this<NSQueryData>
{
public:
	NSQueryData();
	NSQueryData(const char* function, uint64_t line,
		std::shared_ptr<NSDBSession> session = {});
	NSQueryData(const wchar_t* functionName, int line,
		const TYPE_QUERY_FUNC& queryFunc,
		const TYPE_RESULT_FUNC& resultFunc,
		std::shared_ptr<NSDBSession> session = {});

	virtual ~NSQueryData();
	void Reset();

	std::shared_ptr<NSDBSession> GetDBSession() const { return m_pSession; }
	TYPE_RESULT_FUNC GetResultFunc() const { return m_pResultFunc; }

	template<typename Ty>
	std::shared_ptr<Ty> GetSession() const
	{
		return std::dynamic_pointer_cast<Ty>(m_pSession);
	}

	virtual void RunProcQuery() override;
	virtual void RunResultQuery() override;

private:
	TYPE_QUERY_FUNC  m_pQueryFunc;
	TYPE_RESULT_FUNC m_pResultFunc;
	std::shared_ptr<NSDBSession> m_pSession;
	const char* m_Function = nullptr;
	uint64_t m_Line = 0;
};
