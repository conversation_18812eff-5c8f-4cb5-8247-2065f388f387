#include "stdafx.h"
#include "RecordSet.h"
#include <cstring>
#include <vector>
#include <stdexcept>

RecordSet::RecordSet(MYSQL* mysql, MYSQL_RES* result)
    : m_mysql(mysql), m_result(Database::make_mysql_result(result))
{
    if (m_result)
    {
        m_fieldCount = mysql_num_fields(m_result.get());
        InitFieldMapping();
    }
}

RecordSet::RecordSet(MYSQL_STMT* stmt, MYSQL_RES* metadata)
    : m_stmt(stmt), m_result(Database::make_mysql_result(metadata))
{
    if (!m_result || !m_stmt)
    {
        throw std::runtime_error("Invalid statement or result set");
    }
    
    m_fieldCount = mysql_num_fields(m_result.get());
    if (m_fieldCount <= 0)
    {
        throw std::runtime_error("No fields in result set");
    }
    
    InitFieldMapping();
    
    // Statement 바인딩 준비
    m_binds = mi::make_unique_array<MYSQL_BIND>(m_fieldCount);
    m_lengths = mi::make_unique_array<unsigned long>(m_fieldCount);
    m_isNull = mi::make_unique_array<mysql_bool_compat>(m_fieldCount);
    
    memset(m_binds.get(), 0, sizeof(MYSQL_BIND) * m_fieldCount);
    
    // 필드별 동적 버퍼 할당
    MYSQL_FIELD* fields = mysql_fetch_fields(m_result.get());
    if (!fields)
    {
        throw std::runtime_error("Failed to fetch field information");
    }
    
    // 필드별 버퍼 크기 계산
    size_t totalBufferSize = 0;
    mi::vector<size_t> bufferSizes(m_fieldCount);
    
    for (int i = 0; i < m_fieldCount; ++i)
    {
        // 필드 타입에 따른 적절한 버퍼 크기 결정
        size_t bufferSize = 0;
        switch (fields[i].type)
        {
            case MYSQL_TYPE_TINY:
            case MYSQL_TYPE_SHORT:
            case MYSQL_TYPE_LONG:
            case MYSQL_TYPE_INT24:
                bufferSize = 16;
                break;
            case MYSQL_TYPE_LONGLONG:
                bufferSize = 32;
                break;
            case MYSQL_TYPE_FLOAT:
            case MYSQL_TYPE_DOUBLE:
                bufferSize = 64;
                break;
            case MYSQL_TYPE_STRING:
            case MYSQL_TYPE_VAR_STRING:
            case MYSQL_TYPE_BLOB:
            case MYSQL_TYPE_TINY_BLOB:
            case MYSQL_TYPE_MEDIUM_BLOB:
            case MYSQL_TYPE_LONG_BLOB:
                // max_length가 0이면 기본값 사용
                bufferSize = fields[i].max_length > 0 ? fields[i].max_length + 1 : 4096;
                break;
            default:
                bufferSize = 1024; // 기본값
                break;
        }
        
        bufferSizes[i] = bufferSize;
        totalBufferSize += bufferSize;
    }
    
    // 전체 버퍼 할당 (mimalloc 사용)
    m_bindBuffer = mi::make_unique_array<char>(totalBufferSize);
    memset(m_bindBuffer.get(), 0, totalBufferSize);
    
    // 개별 필드 바인딩 설정
    char* bufferPtr = m_bindBuffer.get();
    for (int i = 0; i < m_fieldCount; ++i)
    {
        m_binds[i].buffer_type = fields[i].type;
        m_binds[i].buffer = bufferPtr;
        m_binds[i].buffer_length = static_cast<unsigned long>(bufferSizes[i]);
        m_binds[i].length = &m_lengths[i];
        m_binds[i].is_null = &m_isNull[i];
        
        bufferPtr += bufferSizes[i];
    }
    
    if (mysql_stmt_bind_result(m_stmt, m_binds.get()) != 0)
    {
        throw std::runtime_error(std::string("Failed to bind statement result: ") + mysql_stmt_error(m_stmt));
    }
}

RecordSet::~RecordSet()
{
    // 결과는 Connection에서 관리하므로 여기서는 해제하지 않음
}

bool RecordSet::IsEOF()
{
    // 첫 호출 시 첫 번째 행으로 이동
    if (m_firstCall)
    {
        m_firstCall = false;
        return !MoveNext();
    }
    
    // 이미 EOF인 경우
    if (m_isEof)
        return true;
    
    // 다음 행으로 자동 이동
    return !MoveNext();
}

bool RecordSet::MoveNext()
{
    if (m_stmt)
    {
        // PreparedStatement인 경우
        int fetchResult = mysql_stmt_fetch(m_stmt);
        if (fetchResult == 0)
        {
            return true;
        }
        else if (fetchResult == MYSQL_NO_DATA)
        {
            m_isEof = true;
            return false;
        }
        else
        {
            // 에러 처리
            m_isEof = true;
            return false;
        }
    }
    else if (m_mysql && m_result)
    {
        // 일반 쿼리인 경우
        m_currentRow = mysql_fetch_row(m_result.get());
        if (m_currentRow)
        {
            return true;
        }
        else
        {
            m_isEof = true;
            return false;
        }
    }
    
    m_isEof = true;
    return false;
}

bool RecordSet::GetItem(const std::string& fieldName, int32_t& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(const std::string& fieldName, int64_t& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(const std::string& fieldName, float& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(const std::string& fieldName, double& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(const std::string& fieldName, std::string& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(const std::string& fieldName, std::vector<uint8_t>& value)
{
    int index = GetFieldIndex(fieldName);
    if (index < 0)
        return false;
    return GetItem(index, value);
}

bool RecordSet::GetItem(int fieldIndex, int32_t& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        // 타입에 따라 변환
        MYSQL_FIELD* fields = mysql_fetch_fields(m_result.get());
        switch (fields[fieldIndex].type)
        {
        case MYSQL_TYPE_TINY:
        case MYSQL_TYPE_SHORT:
        case MYSQL_TYPE_LONG:
        case MYSQL_TYPE_INT24:
            value = *reinterpret_cast<int32_t*>(m_binds[fieldIndex].buffer);
            return true;
        case MYSQL_TYPE_LONGLONG:
            value = static_cast<int32_t>(*reinterpret_cast<int64_t*>(m_binds[fieldIndex].buffer));
            return true;
        default:
            return false;
        }
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        try
        {
            value = std::stoi(m_currentRow[fieldIndex]);
            return true;
        }
        catch (const std::exception&)
        {
            // 변환 실패 시 0 반환
            value = 0;
            return false;
        }
    }
    
    return false;
}

bool RecordSet::GetItem(int fieldIndex, int64_t& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        MYSQL_FIELD* fields = mysql_fetch_fields(m_result.get());
        switch (fields[fieldIndex].type)
        {
        case MYSQL_TYPE_LONGLONG:
            value = *reinterpret_cast<int64_t*>(m_binds[fieldIndex].buffer);
            return true;
        case MYSQL_TYPE_TINY:
        case MYSQL_TYPE_SHORT:
        case MYSQL_TYPE_LONG:
        case MYSQL_TYPE_INT24:
            value = static_cast<int64_t>(*reinterpret_cast<int32_t*>(m_binds[fieldIndex].buffer));
            return true;
        default:
            return false;
        }
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        try
        {
            value = std::stoll(m_currentRow[fieldIndex]);
            return true;
        }
        catch (const std::exception&)
        {
            // 변환 실패 시 0 반환
            value = 0;
            return false;
        }
    }
    
    return false;
}

bool RecordSet::GetItem(int fieldIndex, float& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        MYSQL_FIELD* fields = mysql_fetch_fields(m_result.get());
        if (fields[fieldIndex].type == MYSQL_TYPE_FLOAT)
        {
            value = *reinterpret_cast<float*>(m_binds[fieldIndex].buffer);
            return true;
        }
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        try
        {
            value = std::stof(m_currentRow[fieldIndex]);
            return true;
        }
        catch (const std::exception&)
        {
            // 변환 실패 시 0.0f 반환
            value = 0.0f;
            return false;
        }
    }
    
    return false;
}

bool RecordSet::GetItem(int fieldIndex, double& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        MYSQL_FIELD* fields = mysql_fetch_fields(m_result.get());
        if (fields[fieldIndex].type == MYSQL_TYPE_DOUBLE)
        {
            value = *reinterpret_cast<double*>(m_binds[fieldIndex].buffer);
            return true;
        }
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        try
        {
            value = std::stod(m_currentRow[fieldIndex]);
            return true;
        }
        catch (const std::exception&)
        {
            // 변환 실패 시 0.0 반환
            value = 0.0;
            return false;
        }
    }
    
    return false;
}

bool RecordSet::GetItem(int fieldIndex, std::string& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        value.assign(reinterpret_cast<char*>(m_binds[fieldIndex].buffer), m_lengths[fieldIndex]);
        return true;
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        value = m_currentRow[fieldIndex];
        return true;
    }
    
    return false;
}

bool RecordSet::GetItem(int fieldIndex, std::vector<uint8_t>& value)
{
    if (fieldIndex < 0 || fieldIndex >= m_fieldCount)
        return false;
    
    if (m_stmt)
    {
        if (m_isNull[fieldIndex])
            return false;
        
        uint8_t* data = reinterpret_cast<uint8_t*>(m_binds[fieldIndex].buffer);
        value.assign(data, data + m_lengths[fieldIndex]);
        return true;
    }
    else if (m_currentRow)
    {
        if (!m_currentRow[fieldIndex])
            return false;
        
        unsigned long* lengths = mysql_fetch_lengths(m_result.get());
        if (lengths)
        {
            uint8_t* data = reinterpret_cast<uint8_t*>(m_currentRow[fieldIndex]);
            value.assign(data, data + lengths[fieldIndex]);
            return true;
        }
    }
    
    return false;
}

const char* RecordSet::GetFieldName(int index) const
{
    if (!m_result || index < 0 || index >= m_fieldCount)
        return nullptr;
    
    MYSQL_FIELD* fields = mysql_fetch_fields(m_result);
    return fields[index].name;
}

int RecordSet::GetFieldIndex(const std::string& fieldName)
{
    auto it = m_fieldMapping.find(mi::string(fieldName));
    if (it != m_fieldMapping.end())
        return it->second;
    return -1;
}

void RecordSet::InitFieldMapping()
{
    if (!m_result)
        return;
    
    MYSQL_FIELD* fields = mysql_fetch_fields(m_result);
    for (int i = 0; i < m_fieldCount; ++i)
    {
        m_fieldMapping[mi::string(fields[i].name)] = i;
    }
}