{"permissions": {"allow": ["Bash(rm:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(chmod:*)", "WebFetch(domain:github.com)", "WebFetch(domain:mariadb.com)", "<PERSON><PERSON>(touch:*)", "Bash(ls:*)", "Bash(# Extract all class names\ngrep -h \"\"^class\\s\\+\\w\\+\"\" /mnt/d/dblib_new/Database_Maria/**/*.h | sed ''s/class\\s\\+\\([[:alnum:]_]\\+\\).*/\\1/'' | sort | uniq > /tmp/all_classes.txt\n\n# Check which classes are referenced\nwhile read classname; do\n    count=$(grep -r \"\"\\b$classname\\b\"\" /mnt/d/dblib_new/Database_Maria --include=\"\"*.cpp\"\" --include=\"\"*.h\"\" | grep -v \"\"^.*:class\\s\\+$classname\"\" | wc -l)\n    if [ $count -eq 0 ]; then\n        echo \"\"Potentially unused class: $classname\"\"\n    fi\ndone < /tmp/all_classes.txt)", "Bash(# Find all header files\nfind /mnt/d/dblib_new/Database_Maria -name \"\"*.h\"\" | while read header; do\n    basename_h=$(basename \"\"$header\"\")\n    # Check if this header is included anywhere\n    if ! grep -r \"\"#include.*$basename_h\"\" /mnt/d/dblib_new/Database_Maria --include=\"\"*.cpp\"\" --include=\"\"*.h\"\" > /dev/null; then\n        echo \"\"Potentially unused header: $header\"\"\n    fi\ndone)", "Bash(# Check for .cpp files without corresponding .h files\nfind /mnt/d/dblib_new/Database_Maria -name \"\"*.cpp\"\" | while read cpp; do\n    base=$(basename \"\"$cpp\"\" .cpp)\n    dir=$(dirname \"\"$cpp\"\")\n    if [ ! -f \"\"$dir/$base.h\"\" ]; then\n        echo \"\"CPP without header: $cpp\"\"\n    fi\ndone\n\necho \"\"---\"\"\n\n# Check for .h files without corresponding .cpp files\nfind /mnt/d/dblib_new/Database_Maria -name \"\"*.h\"\" | while read header; do\n    base=$(basename \"\"$header\"\" .h)\n    dir=$(dirname \"\"$header\"\")\n    if [ ! -f \"\"$dir/$base.cpp\"\" ] && [ \"\"$base\"\" != \"\"stdafx\"\" ]; then\n        echo \"\"Header without implementation: $header\"\"\n    fi\ndone)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(diff:*)", "Bash(g++:*)", "WebFetch(domain:dev.mysql.com)", "<PERSON><PERSON>(comm:*)", "<PERSON><PERSON>(mkdir:*)"], "deny": []}}