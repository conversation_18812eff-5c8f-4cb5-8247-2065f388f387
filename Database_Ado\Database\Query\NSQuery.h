#pragma once
#include "NPModels.h"

#include "NSDefineEnum.h"
#include "DataBase/NSDataBaseManager.h"

#include "ADO/NSAdoRecordset.h"

class NSQueryData;
class NSQuery
{
public:
	NSQuery(EDataBase dbType, std::string_view query);

	auto QueryFunc(const std::shared_ptr<NSQueryData> queryData) -> EErrorCode;

protected:
	EDataBase m_DBType{ EDataBase::Game };
	std::string m_Query;
};

template<size_t Length>
class NSFixedSizeQuery
{
public:
	NSFixedSizeQuery(EDataBase dbType, std::string_view query)
	{
		m_DBType = dbType;
		strncpy_s(m_Query, _countof(m_Query), query.data(), _TRUNCATE);
	}

	auto QueryFunc(const std::shared_ptr<NSQueryData> queryData) -> EErrorCode
	{
		NSAdoConnection* connection = NSDataBaseManager::GetInstance()->GetDBConnection(m_DBType);
		if (connection == nullptr)
			return EErrorCode::DBConnectionError;

		NSAdoRecordset* recordSet = queryData->GetAdoRecordSet();
		if (recordSet == nullptr)
			return EErrorCode::DBError;

		if (!recordSet->Open(connection, m_Query))
			return EErrorCode::DBError;

		return EErrorCode::None;
	}

protected:
	EDataBase m_DBType{ EDataBase::Game };
	char m_Query[Length];
};

using NSSmallQuery = NSFixedSizeQuery<256>;
using NSMediumQuery = NSFixedSizeQuery<1024>;
using NSLargeQuery = NSFixedSizeQuery<4096>;
