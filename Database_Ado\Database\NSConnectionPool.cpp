#include "stdafx.h"
#include "NSConnectionPool.h"

NSConnectionPool::NSConnectionPool(const std::string_view host, uint32_t port,
	const std::string_view dbName, const std::string_view user, const std::string_view password) :
	m_Host(host), m_Port(port), m_DBName(dbName), m_UserID(user), m_Password(password)
{
}

auto NSConnectionPool::GetHost() const->const char*
{
	return m_Host.c_str();
}

auto NSConnectionPool::GetPort() const->uint32_t
{
	return m_Port;
}

auto NSConnectionPool::GetDBName() const->const char*
{
	return m_DBName.c_str();
}

auto NSConnectionPool::GetUserID() const->const char*
{
	return m_UserID.c_str();
}

auto NSConnectionPool::GetPassword() const->const char*
{
	return m_Password.c_str();
}
