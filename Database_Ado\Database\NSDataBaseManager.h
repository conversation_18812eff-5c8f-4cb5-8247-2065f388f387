#pragma once

#include "NSDefineEnum.h"

#include "NSDBSession.h"

#include "ADO/NSAdoConnection.h"
#include "DataBase/NSQueryData.h"
#include "DataBase/Storage/NSStorageUpdateContainer.h"
#include "QueryData/NSDataSerializer.h"
#include "NSUtil/NSElapsedTimeUtil.h"

#include "Promise/NSPromise.h"
#include "Win32/NSThread.h"
#include "../../../../Net/Source/Runtime/CNL/CNLServerInfo.h"

class NSConnectionPool;
struct NSStorageUpdateContainer;

class NSDataBaseManager : public TemplateSingleton<NSDataBaseManager>
{
	struct NSDataBaseExecutor : public Promise::NSExecutor
	{
		bool RunningInThisThread() const override;

		void Post(const std::function<void()>& function) override;

		std::thread::id ThreadId;
		Concurrency::concurrent_queue<std::function<void()>> Queue;
		int64_t QueueSize = 0;
	};

public:
	static const int MAX_RECONNECT_ATTEMPTS = 3;
	static const int RECONNECT_DELAY_MS = 1000; // 1초
	static const int HEAVY_QUERY_THRESHOLD_MS = 1000;

	NSDataBaseManager();
	virtual ~NSDataBaseManager();

public:
	void SetDBConnectionList();
	void ReleaseDBConnectionList();

	bool Start(uint32_t workThreadCnt);
	bool Stop();

	void ProhibitPushAccess();
	void StopAllWorkerThreadAndWait();

	bool AddConnectionInfo(EDataBase dbType, const EDBProvider provider, const std::string_view host, uint32_t port, const std::string_view dbName,
		const std::string_view user, const std::string_view password, int32_t initPoolCount);

	auto GetDBConnection(EDataBase dbType) const->NSAdoConnection*;
	const std::optional<uint32_t> GetDBThreadCount() const { return m_WorkThreadCount; };
	bool ReconnectConnection(EDataBase dbType, NSAdoConnection* connection) const;

	void StorageUpdateQuery(
		std::shared_ptr<NSStorageUpdateContainer> containerData,
		std::function<EErrorCode(const std::shared_ptr<NSQueryData>, const std::shared_ptr<NSStorageUpdateContainer>)> pQueryFunc,
		std::function<EErrorCode(const std::shared_ptr<NSQueryData>, const std::shared_ptr<NSStorageUpdateContainer>)> pResultFunc,
		std::shared_ptr<NSDBSession> session = nullptr,
		std::source_location location = std::source_location::current()
	);

	template <typename Sp>
	void StorageUpdateQueryWithCustomProcedure(
		std::shared_ptr<NSStorageUpdateContainer> containerData,
		NSDataSerializer& dataSerializer,
		std::function<EErrorCode(const std::shared_ptr<NSQueryData>, const std::shared_ptr<NSStorageUpdateContainer>)> pResultFunc,
		std::shared_ptr<NSDBSession> session = nullptr,
		std::source_location location = std::source_location::current()
	)
	{
		if (m_PushAccessProhibit.load() == true)
			return;

		m_PushAccessCnt.fetch_add(1);

		auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);
		queryData->GetQueryData() = std::move(dataSerializer);

		CNLServerInfo::GetInstance()->IncDatabaseQueryPostCount(1);
		m_QueriesProcessingCount.fetch_add(1);
		m_NumInputForLog.fetch_add(1);

		Post(GetExecutorByShardKey(containerData->Cid),
			[this, queryData, containerData, onAfterExecuteQuery = m_OnAfterExecuteQuery]()
			{
				NSElapsedTimeUtil::ScopedDuration timer{ queryData, onAfterExecuteQuery };

				Sp* spData = queryData->GetQueryData().Front<Sp>();
				if (spData == nullptr)
				{
					LOGF << "Failed to get data from the buffer";
					queryData->SetErrorCode(EErrorCode::DBArgumentError);
				}
				else
				{
					queryData->SetErrorCode(spData->StorageQueryFunc(queryData, containerData));
				}

				if (queryData->GetErrorCode() == EErrorCode::None)
				{
					CNLServerInfo::GetInstance()->IncDatabaseQuerySucceededCount(1);
				}
				else
				{
					CNLServerInfo::GetInstance()->GetDatabaseQueryFailedCount(1);
				}

				m_QueriesProcessingCount.fetch_sub(1);
				m_NumOutputForLog.fetch_add(1);
			})
			.Then([queryData, containerData, pResultFunc]()
				{
					queryData->SetErrorCode(pResultFunc(queryData, containerData));
				});

		m_PushAccessCnt.fetch_sub(1);
	}

	template <typename Sp>
	auto StartQuery(
		std::shared_ptr<NSDBSession> session,
		std::source_location location = std::source_location::current())
		-> NSPromise<std::shared_ptr<NSQueryData>>
	{
		auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);

		return StartQuery<Sp>(std::move(queryData), session->GetAID());
	}

	template <typename Sp>
	auto StartQuery(
		std::shared_ptr<NSDBSession> session,
		NSDataSerializer& dataSerializer,
		std::source_location location = std::source_location::current())
		-> NSPromise<std::shared_ptr<NSQueryData>>
	{
		auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);
		queryData->GetQueryData() = std::move(dataSerializer);

		return StartQuery<Sp>(std::move(queryData), session->GetAID());
	}

	template <typename Sp>
	auto StartQuery(
		NSDataSerializer& dataSerializer,
		uint64_t uiShardKey = 0,
		std::source_location location = std::source_location::current())
		-> NSPromise<std::shared_ptr<NSQueryData>>
	{
		auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line());
		queryData->GetQueryData() = std::move(dataSerializer);

		return StartQuery<Sp>(std::move(queryData), uiShardKey);
	}

	template <typename Sp>
	auto StartQuery(std::shared_ptr<NSQueryData> queryData, uint64_t uiShardKey)
		-> NSPromise<std::shared_ptr<NSQueryData>>
	{
		if (m_PushAccessProhibit.load() == true)
			return NSPromise<std::shared_ptr<NSQueryData>>();

		CNLServerInfo::GetInstance()->IncDatabaseQueryPostCount(1);
		m_QueriesProcessingCount.fetch_add(1);
		m_NumInputForLog.fetch_add(1);

		return Post(GetExecutorByShardKey(uiShardKey), [this, queryData, onAfterExecuteQuery = m_OnAfterExecuteQuery]()
			{
				NSElapsedTimeUtil::ScopedDuration timer{ queryData, onAfterExecuteQuery };

				// NSDataSerializer의 맨앞은 Sp 보장
				Sp* spData = queryData->GetQueryData().Front<Sp>();
				if (spData == nullptr)
				{
					LOGF << "Failed to get data from the buffer";
					queryData->SetErrorCode(EErrorCode::DBArgumentError);
				}
				else
				{
					queryData->SetErrorCode(spData->QueryFunc(queryData));
				}

				if (queryData->GetErrorCode() == EErrorCode::None)
				{
					CNLServerInfo::GetInstance()->IncDatabaseQuerySucceededCount(1);
				}
				else
				{
					CNLServerInfo::GetInstance()->IncDatabaseQueryFailedCount(1);
				}

				m_QueriesProcessingCount.fetch_sub(1);
				m_NumOutputForLog.fetch_add(1);
				return queryData;
			});
	}

	auto GetQueriesProcessingCount() const -> int32_t { return m_QueriesProcessingCount; }
	auto GetDBQueueSize() const->int64_t;
	std::string GetConnectionPoolCountInfo() const;
	std::string GetConnectionPoolCountLog() const;

	auto GetConnectionPool(EDataBase dbType) const->NSConnectionPool*;

	template<typename Fn>
	void ForEach(Fn func)
	{
		for (auto connection : m_pcAdoConnectionPool)
		{
			if (nullptr == connection)
			{
				continue;
			}

			func(connection);
		}
	}

	void SetAfterExecuteQuery(const std::function<void(const std::shared_ptr<NSQueryData>&)>& onAfterExecuteQuery);

	uint64_t GetInputCount() const
	{
		return m_NumInputForLog;
	}

	uint64_t GetOutputCount() const
	{
		return m_NumOutputForLog;
	}

	void ResetIOCount()
	{
		m_NumInputForLog.store(0);
		m_NumOutputForLog.store(0);
	}

protected:
	bool CheckDbType(EDataBase dbType) const;

	//쿼리 처리
	void WorkThreadProc(int iThreadIndex, HANDLE hEvent);

private:
	auto GetExecutorByShardKey(uint64_t shardKey) -> Promise::NSExecutor&;

private:
	bool					 m_IsRunning{ false };
	std::vector<NSThread>    m_WorkThreads;

	std::atomic<bool> m_PushAccessProhibit = false;
	std::atomic<int> m_PushAccessCnt = 0;

	std::atomic<int32_t> m_QueriesProcessingCount{ 0 };			// 처리중인 쿼리 개수
	std::atomic<uint64_t> m_NumInputForLog{ 0 };
	std::atomic<uint64_t> m_NumOutputForLog{ 0 };

	NSConnectionPool* m_pcAdoConnectionPool[(int)EDataBase::End];
	std::vector<std::unique_ptr<NSDataBaseExecutor>> m_DataBaseExecutors;

	std::function<void(const std::shared_ptr<NSQueryData>&)> m_OnAfterExecuteQuery;		// Execute 후 호출
	std::optional<uint32_t> m_WorkThreadCount = std::nullopt;
};
