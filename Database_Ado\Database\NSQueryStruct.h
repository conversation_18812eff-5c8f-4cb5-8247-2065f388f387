#pragma once

struct NSRecord_Item
{
	int64_t		ItemUid           = 0;
	int32_t		ItemId            = 0;
	uint8_t		Location          = 0;
	int32_t		Quantity          = 0;
	bool		IsMarketPreserved = false;
	uint64_t	UpdateAt          = 0;
	uint64_t	CreateAt          = 0;
	uint8_t		BelongType        = 0;
	int32_t		Quality			  = 0;
};

struct NSRecord_ItemOption
{
	int64_t ItemUid = 0;
	uint16_t OptionKey = 0;
	uint16_t OptionIdx = 0;
	int64_t OptionValue = 0;
};

struct NSRecord_InventorySlot
{
	int64_t ItemUid = 0;
	int32_t ItemId = 0;
	uint8_t Slot = 0;
};
