#pragma once
#include <type_traits>
#include <utility>
#include <functional>

namespace Promise::Detail
{
	template <typename T>
	struct MemberFunctionTraits;

	template <typename TReturn, typename TClass, typename... Args>
	struct MemberFunctionTraits<TReturn(TClass::*)(Args...) const>
	{
		using return_type = TReturn;
		using class_type = TClass;
		using args_type = std::tuple<Args...>;

		template <size_t Index>
		using arg_type = std::tuple_element_t<Index, args_type>;

		static constexpr size_t Arity = sizeof... (Args);

		using function_type = std::function<return_type(Args...)>;
	};

	template <typename TReturn, typename TClass, typename... Args>
	struct MemberFunctionTraits<TReturn(TClass::*)(Args...)>
	{
		using return_type = TReturn;
		using class_type = TClass;
		using args_type = std::tuple<Args...>;

		template <size_t Index>
		using arg_type = std::tuple_element_t<Index, args_type>;

		static constexpr size_t Arity = sizeof... (Args);

		using function_type = std::function<return_type(Args...)>;
	};

	template <typename T>
	struct FunctionTraits;

	template <typename TReturn, typename... Args>
	struct FunctionTraits<TReturn(*)(Args...)>
	{
		using return_type = TReturn;
		using args_type = std::tuple<Args...>;

		template <size_t Index>
		using arg_type = std::tuple_element_t<Index, args_type>;

		static constexpr size_t Arity = sizeof... (Args);

		using function_type = std::function<return_type(Args...)>;
	};

	template <typename T>
	struct BindTraits;

	template <typename TReturn, typename TFx, typename... Args>
	struct BindTraits<std::_Binder<TReturn, TFx, Args...>>
	{
		using return_type = std::invoke_result_t<TFx, Args...>;
		using args_type = std::tuple<Args...>;

		template <size_t Index>
		using arg_type = std::tuple_element_t<Index, args_type>;

		static constexpr size_t Arity = sizeof... (Args);

		using function_type = std::function<return_type(Args...)>;
	};

	template <typename T, typename = void, bool = std::is_bind_expression_v<std::decay_t<T>>>
	struct Traits;

	template <typename T, typename U>
	struct Traits<T, U, true> : BindTraits<std::decay_t<T>> {};

	template <typename T, typename U>
	struct Traits<T, U, false> : FunctionTraits<std::decay_t<T>> {};

	template <typename T>
	struct Traits<T, std::void_t<decltype(&std::decay_t<T>::operator())>, false> : MemberFunctionTraits<decltype(&std::decay_t<T>::operator())> {};
}