#include "stdafx.h"
#include "NSCompletionToken.h"

#include <iostream>

namespace Detail
{
	CompletionTokenBase::~CompletionTokenBase()
	{
	}

	bool CompletionTokenBase::IsDone() const
	{
		std::lock_guard lock(m_Mutex);

		return Unsafe_IsDone();
	}

	void CompletionTokenBase::Wait()
	{
		std::unique_lock lock(m_Mutex);

		m_CondVar.wait(lock, [this]()
			{
				return Unsafe_IsDone();
			});
	}

	void CompletionTokenBase::WaitFor(const std::chrono::milliseconds& milliseconds)
	{
		std::unique_lock lock(m_Mutex);

		m_CondVar.wait_for(lock, milliseconds, [this]()
			{
				return Unsafe_IsDone();
			});
	}

	void CompletionTokenBase::AddCompletionCallback(const std::function<void()>& continuation)
	{
		std::unique_lock lock(m_Mutex);

		if (Unsafe_IsDone())
		{
			lock.unlock();
			continuation();
		}
		else
		{
			m_CompletionCallbacks.push_back(continuation);
		}
	}

	bool CompletionTokenBase::Unsafe_IsDone() const
	{
		return m_Done;
	}

}

NSCompletionToken<void>::NSCompletionToken(NullConstructToken)
	: m_Impl(nullptr)
{
}

bool NSCompletionToken<void>::IsValid() const
{
	return m_Impl.operator bool();
}

bool NSCompletionToken<void>::IsDone() const
{
	return m_Impl->IsDone();
}

void NSCompletionToken<void>::Get()
{
	return m_Impl->Get();
}

void NSCompletionToken<void>::Set()
{
	return m_Impl->Set();
}

void NSCompletionToken<void>::Wait()
{
	m_Impl->Wait();
}

void NSCompletionToken<void>::WaitFor(const std::chrono::milliseconds& milliseconds)
{
	m_Impl->WaitFor(milliseconds);
}

void NSCompletionToken<void>::AddCompletionCallback(const std::function<void()>& callback)
{
	m_Impl->AddCompletionCallback(callback);
}
