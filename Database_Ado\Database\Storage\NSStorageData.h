#pragma once

template <typename _Type> class NSStorageData
{
public:
	NSStorageData() {}
	NSStorageData(const bool DirtyFlagOn)
	{
		if (DirtyFlagOn)
			SetDirty();
	}

	NSStorageData(const _Type& defaultValue, const bool DirtyFlagOn = false)
	{
		m_ContainValue = defaultValue;
		if (DirtyFlagOn)
			SetDirty();
	}

	const _Type& Get() const
	{
		return m_ContainValue;
	}

	const _Type& GetOrigin() const
	{
		return m_Origin;
	}

	_Type& Load()
	{
		return m_ContainValue;
	}

	_Type& Set()
	{
		SetDirty();

		return m_ContainValue;
	}

	_Type& SetInSandbox()
	{
		SetSandbox();

		return m_ContainValue;
	}

	void ClearDirty()
	{
		m_isDirty = false;
		m_Origin = m_ContainValue;
	}

	void ClearSandbox()
	{
		m_isSandbox = false;
		m_ContainValue = m_Origin;
	}

	bool IsDirty() const
	{
		return IsSandbox()
			? false
			: m_isDirty;
	}

	bool IsSandbox() const
	{
		return m_isSandbox;
	}

	void Rollback()
	{
		if (!IsDirty())
			return;
		m_ContainValue = m_Origin;
	}

private:
	void SetDirty()
	{
		if (IsSandbox())
			return;

		if (!IsDirty())
			m_Origin = m_ContainValue;
		m_isDirty = true;
	}

	void SetSandbox()
	{
		if (!IsSandbox())
			m_Origin = m_ContainValue;
		m_isSandbox = true;
	}

	// 	NSStorageData<_Type>& operator =(const NSStorageData<_Type>&)
	// 	{
	// 		assert(false && "DO NOT USE EQUAL OPERATOR IN NSStorageData");
	// 	}

private:
	bool m_isDirty = false;
	bool m_isSandbox = false;
	_Type m_ContainValue{};

	_Type m_Origin{};
};

template <typename _Type> class NSStorageData<std::unique_ptr<_Type>>
{
public:
	NSStorageData() = default;

	NSStorageData(const bool DirtyFlagOn)
	{
		if (DirtyFlagOn)
			SetDirty();
	}

	NSStorageData(std::unique_ptr<_Type>&& defaultValue, const bool DirtyFlagOn = false)
	{
		m_ContainValue = std::move(defaultValue);
		if (DirtyFlagOn)
			SetDirty();
	}

	const std::unique_ptr<_Type>& Get() const
	{
		return m_ContainValue;
	}

	const std::unique_ptr<_Type>& GetOrigin() const
	{
		return m_Origin;
	}

	std::unique_ptr<_Type>& Load()
	{
		return m_ContainValue;
	}

	std::unique_ptr<_Type>& Set()
	{
		SetDirty();

		return m_ContainValue;
	}

	std::unique_ptr<_Type>& SetInSandbox()
	{
		SetSandbox();

		return m_ContainValue;
	}

	void ClearDirty()
	{
		m_isDirty = false;
		if (m_ContainValue)
		{
			m_Origin = std::make_unique<_Type>(*m_ContainValue);
		}
	}

	void ClearSandbox()
	{
		m_isSandbox = false;
		if (m_Origin)
		{
			m_ContainValue = std::make_unique<_Type>(*m_Origin);
		}
	}

	bool IsDirty() const
	{
		return IsSandbox()
			? false
			: m_isDirty;
	}

	bool IsSandbox() const
	{
		return m_isSandbox;
	}

	void Rollback()
	{
		if (!IsDirty() || m_ContainValue == nullptr)
			return;

		if (m_ContainValue == nullptr)
		{
			m_ContainValue = std::make_unique<_Type>(*m_Origin);
		}
		else
		{
			*m_ContainValue = *m_Origin;
		}
	}

	void SetOnlySandboxFlag()
	{
		m_isSandbox = true;
	}

	void UnSetOnlySandboxFlag()
	{
		m_isSandbox = false;
	}

private:
	void SetDirty()
	{
		if (IsSandbox())
			return;

		if (!IsDirty() && m_ContainValue != nullptr)
		{
			m_Origin = std::make_unique<_Type>(*m_ContainValue);
		}
		m_isDirty = true;
	}

	void SetSandbox()
	{
		if (!IsSandbox() && m_ContainValue != nullptr)
		{
			m_Origin = std::make_unique<_Type>(*m_ContainValue);
		}
		m_isSandbox = true;
	}

	// 	NSStorageData<std::unique_ptr<_Type>>& operator =(const NSStorageData<std::unique_ptr<_Type>>& inValue)
	// 	{
	// 		assert(false && "DO NOT USE EQUAL OPERATOR IN NSStorageData");
	// 	}

private:
	bool m_isDirty = false;
	bool m_isSandbox = false;
	std::unique_ptr<_Type> m_ContainValue;
	std::unique_ptr<_Type> m_Origin;
};

template <typename _Key, typename _Container> class NSStorageDataMap
{
public:
	NSStorageDataMap() = default;

	const std::unordered_map<_Key, NSStorageData<_Container>>& Get() const
	{
		return m_ContainValue;
	}

	const std::unordered_map<_Key, NSStorageData<_Container>>& GetPendingRemove() const
	{
		return m_PendingRemove;
	}

	const NSStorageData<_Container>& Get(_Key key) const
	{
		const auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.cend())
			return nullObject;
		return it->second;
	}

	const NSStorageData<_Container>& GetOrigin(_Key key) const
	{
		const auto it = m_Origin.find(key);
		if (it == m_Origin.cend())
			return nullObject;
		return it->second;
	}

	void Load(_Key key, _Container value)
	{
		m_ContainValue.emplace(key, NSStorageData<_Container>(value, false));
	}

	const _Container& Set(_Key key, const _Container& value)
	{
		SetDirty();

		auto result = m_ContainValue.emplace(key, NSStorageData<_Container>(value, true));
		if (!result.second)
		{
			result.first->second.Set() = value;
		}
		return result.first->second.Get();
	}

	const _Container& SetInSandbox(_Key key, const _Container& value)
	{
		SetSandbox();

		auto result = m_ContainValue.emplace(key, NSStorageData<_Container>(value, false));
		result.first->second.SetSandbox() = value;
		return result.first->second.GetInSandbox();
	}

	NSStorageData<_Container>* Find(_Key key)
	{
		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
			return nullptr;

		return &it->second;
	}

	const NSStorageData<_Container>* Find(_Key key) const
	{
		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
			return nullptr;

		return &it->second;
	}

	void ForEach(std::function<void(_Container&)> fnEach)
	{
		SetDirty();
		for (auto& [_, value] : m_ContainValue)
		{
			fnEach(value.Set());
		}
	}

	bool Erase(_Key key)
	{
		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
			return false;

		if (!IsSandbox())
		{
			SetDirty();
			if (!m_PendingRemove.emplace(key, it->second).second)
				return false;
		}

		m_ContainValue.erase(it);
		return true;
	}

	void Clear()
	{
		if (!IsSandbox())
		{
			SetDirty();
			for (auto& [key, container] : m_ContainValue)
			{
				m_PendingRemove.emplace(key, std::move(container));
			}
		}

		m_ContainValue.clear();
	}

	void ClearDirty()
	{
		m_isDirty = false;
		m_PendingRemove.clear();
		m_Origin.clear();
	}

	void ClearSandbox()
	{
		m_isSandBox = false;
		m_ContainValue.clear();
	}

	void ClearDirty(_Key key)
	{
		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
			return;

		it->second.ClearDirty();
	}

	bool IsDirty() const
	{
		return m_isDirty;
	}

	bool IsSandbox() const
	{
		return m_isSandBox;
	}

	void Rollback()
	{
		if (!IsDirty())
			return;

		m_PendingRemove.clear();
		if (!m_Origin.empty())
		{
			m_ContainValue.clear();
			m_ContainValue.swap(m_Origin);
		}

		for (auto& it : m_ContainValue | srv::values)
		{
			it.Rollback();
		}
	}

	bool Empty() const
	{
		return m_ContainValue.empty();
	}

private:
	void SetDirty()
	{
		if (IsSandbox())
			return;

		if (!IsDirty())
			m_Origin = m_ContainValue;
		m_isDirty = true;
	}

	void SetSandbox()
	{
		if (!IsSandbox())
			m_Origin = m_ContainValue;
		m_isSandBox = true;
	}

private:
	bool m_isDirty = false;
	bool m_isSandBox = false;
	std::unordered_map<_Key, NSStorageData<_Container>> m_ContainValue;
	std::unordered_map<_Key, NSStorageData<_Container>> m_Origin;
	std::unordered_map<_Key, NSStorageData<_Container>> m_PendingRemove;
	static NSStorageData<_Container> nullObject;
};

template <typename _Key, typename _Container>
NSStorageData<_Container> NSStorageDataMap<_Key, _Container>::nullObject = {};

template <typename _Key, typename _Container> class NSStorageDataMap<_Key, std::unique_ptr<_Container>>
{
public:
	NSStorageDataMap() = default;

	const std::unordered_map<_Key, NSStorageData<std::unique_ptr<_Container>>>& Get() const
	{
		return m_ContainValue;
	}

	const std::unordered_map<_Key, NSStorageData<std::unique_ptr<_Container>>>& GetPendingRemove() const
	{
		return m_PendingRemove;
	}

	const NSStorageData<std::unique_ptr<_Container>>& Get(_Key key) const
	{
		const auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.cend())
			return nullObject;
		return it->second;
	}

	const NSStorageData<std::unique_ptr<_Container>>& GetOrigin(_Key key) const
	{
		const auto it = m_Origin.find(key);
		if (it == m_Origin.cend())
			return nullObject;
		return it->second;
	}

	void Load(_Key key, std::unique_ptr<_Container>&& value)
	{
		m_ContainValue.emplace(key, NSStorageData<std::unique_ptr<_Container>>(std::move(value), false));
	}

	const std::unique_ptr<_Container>& Set(_Key key, std::unique_ptr<_Container>&& value)
	{
		SetDirty();

		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
		{
			auto result = m_ContainValue.emplace(key, NSStorageData<std::unique_ptr<_Container>>(std::move(value), true));
			return result.first->second.Get();
		}
		else
		{
			it->second = NSStorageData<std::unique_ptr<_Container>>(std::move(value), true);
			return it->second.Get();
		}
	}

	const std::unique_ptr<_Container>& SetInSandbox(_Key key, std::unique_ptr<_Container>&& value)
	{
		SetSandbox();

		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
		{
			auto result = m_ContainValue.emplace(key, NSStorageData<std::unique_ptr<_Container>>(std::move(value), false));
			result.first->second.SetSandbox();
			return result.first->second.Get();
		}
		else
		{
			it->second = NSStorageData<std::unique_ptr<_Container>>(std::move(value), false);
			it->second.SetSandbox();
			return it->second.Get();
		}
	}

	const std::unique_ptr<_Container>& SetInSandboxFlag(_Key key, std::unique_ptr<_Container>&& value)
	{
		SetOnlySandboxFlag();

		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
		{
			auto result = m_ContainValue.emplace(key, NSStorageData<std::unique_ptr<_Container>>(std::move(value), false));
			result.first->second.SetOnlySandboxFlag();
			return result.first->second.Get();
		}
		else
		{
			it->second = NSStorageData<std::unique_ptr<_Container>>(std::move(value), false);
			it->second.SetOnlySandboxFlag();
			return it->second.Get();
		}
	}

	NSStorageData<std::unique_ptr<_Container>>* Find(_Key key)
	{
		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
			return nullptr;

		return &it->second;
	}

	void ForEach(std::function<void(_Container&)> fnEach)
	{
		SetDirty();
		for (auto& [_, value] : m_ContainValue)
		{
			fnEach(*value.Set());
		}
	}

	bool Erase(_Key key)
	{
		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
			return false;

		if (!IsSandbox())
		{
			SetDirty();
			if (!m_PendingRemove.emplace(key, std::move(it->second)).second)
				return false;
		}

		m_ContainValue.erase(it);
		return true;
	}

	void Clear()
	{
		if (!IsSandbox())
		{
			SetDirty();
			for (auto& [key, container] : m_ContainValue)
			{
				m_PendingRemove.emplace(key, std::move(container));
			}
		}
		m_ContainValue.clear();
	}

	void ClearDirty()
	{
		m_isDirty = false;
		m_PendingRemove.clear();
		m_Origin.clear();
	}

	void ClearSandbox()
	{
		m_isSandBox = false;
		m_ContainValue.clear();
	}

	void ClearDirty(_Key key)
	{
		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
			return;

		it->second.ClearDirty();
	}

	bool IsDirty() const
	{
		return m_isDirty;
	}

	bool IsSandbox() const
	{
		return m_isSandBox;
	}

	void Rollback()
	{
		if (!IsDirty())
			return;

		m_PendingRemove.clear();
		if (!m_Origin.empty())
		{
			m_ContainValue.clear();
			m_ContainValue.swap(m_Origin);
		}

		for (auto& it : m_ContainValue | srv::values)
		{
			it.Rollback();
		}
	}

	bool Empty() const
	{
		return m_ContainValue.empty();
	}

	bool Freeze(_Key key)
	{
		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
			return false;

		m_Freeze.emplace(key, std::move(it->second));
		m_ContainValue.erase(it);
		return true;
	}

	void Unfreeze()
	{
		for (auto& [key, data] : m_Freeze)
			m_ContainValue.emplace(key, std::move(data));
		m_Freeze.clear();
	}

	void SetOnlySandboxFlag()
	{
		m_isSandBox = true;
	}

	void UnSetOnlySandboxFlag()
	{
		m_isSandBox = false;
	}

private:
	void SetDirty()
	{
		if (IsSandbox())
			return;

		if (!IsDirty())
		{
			for (auto& [key, data] : m_ContainValue)
			{
				if (data.Get() == nullptr)
					continue;

				std::unique_ptr<_Container> value = std::make_unique<_Container>(*data.Get());
				m_Origin.emplace(key, NSStorageData<std::unique_ptr<_Container>>(std::move(value), data.IsDirty()));
			}
		}
		m_isDirty = true;
	}

	void SetSandbox()
	{
		if (!IsSandbox())
		{
			for (auto& [key, data] : m_ContainValue)
			{
				std::unique_ptr<_Container> value = std::make_unique<_Container>(*data.Get());
				m_Origin.emplace(key, NSStorageData<std::unique_ptr<_Container>>(std::move(value), false));
			}
		}
		m_isSandBox = true;
	}

private:
	bool m_isDirty = false;
	bool m_isSandBox = false;
	std::unordered_map<_Key, NSStorageData<std::unique_ptr<_Container>>> m_ContainValue;
	std::unordered_map<_Key, NSStorageData<std::unique_ptr<_Container>>> m_Origin;
	std::unordered_map<_Key, NSStorageData<std::unique_ptr<_Container>>> m_PendingRemove;
	std::unordered_map<_Key, NSStorageData<std::unique_ptr<_Container>>> m_Freeze;
	static NSStorageData<std::unique_ptr<_Container>> nullObject;
};

template <typename _Key, typename _Container>
NSStorageData<std::unique_ptr<_Container>> NSStorageDataMap<_Key, std::unique_ptr<_Container>>::nullObject = {};

template <typename _Key, typename _Container> class NSStorageDataMultiMap
{
public:
	NSStorageDataMultiMap() = default;

	const std::multimap<_Key, NSStorageData<_Container>>& Get() const
	{
		return m_ContainValue;
	}

	const std::multimap<_Key, NSStorageData<_Container>>& GetPendingRemove() const
	{
		return m_PendingRemove;
	}

	const NSStorageData<_Container>& Get(_Key key, std::function<bool(const _Container&)> fnCompare) const
	{
		auto [itBegin, itEnd] = m_ContainValue.equal_range(key);

		for (auto it = itBegin; it != itEnd; ++it)
		{
			if (fnCompare(it->second.Get()))
				return it->second;
		}
		return nullObject;
	}

	void GetRange(_Key key, OUT std::vector<_Container>& result) const
	{
		auto [itBegin, itEnd] = m_ContainValue.equal_range(key);
		result.reserve(std::distance(itBegin, itEnd));

		for (auto it = itBegin; it != itEnd; ++it)
		{
			result.emplace_back(it->second.Get());
		}
	}

	const NSStorageData<_Container>& GetOrigin(_Key key, std::function<bool(const _Container&)> fnCompare) const
	{
		auto [itBegin, itEnd] = m_Origin.equal_range(key);

		for (auto it = itBegin; it != itEnd; ++it)
		{
			if (fnCompare(it->second.Get()))
				return it->second;
		}
		return nullObject;
	}

	void Load(_Key key, _Container value)
	{
		m_ContainValue.emplace(key, NSStorageData<_Container>(value, false));
	}

	const _Container& Add(_Key key, const _Container& value)
	{
		SetDirty();

		return m_ContainValue.emplace(key, NSStorageData<_Container>(value, true))->second.Get();
	}

	const _Container& Set(_Key key, const _Container& value, std::function<bool(const _Container&)> fnCompare)
	{
		SetDirty();
		auto data = m_ContainValue.Find(key, fnCompare);
		if (data == nullptr)
		{
			Add(key, value);
		}
		else
		{
			data->Set() = value;
		}
	}

	NSStorageData<_Container>* Find(_Key key)
	{
		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
			return nullptr;

		return &it->second;
	}

	const NSStorageData<_Container>* Find(_Key key) const
	{
		auto it = m_ContainValue.find(key);
		if (it == m_ContainValue.end())
			return nullptr;

		return &it->second;
	}

	NSStorageData<_Container>* Find(_Key key, std::function<bool(const _Container&)> fnCompare)
	{
		auto [itBegin, itEnd] = m_ContainValue.equal_range(key);
		for (auto it = itBegin; it != itEnd; ++it)
		{
			if (fnCompare(it->second.Get()))
			{
				return &it->second;
			}
		}

		return nullptr;
	}

	const NSStorageData<_Container>* Find(_Key key, std::function<bool(const _Container&)> fnCompare) const
	{
		auto [itBegin, itEnd] = m_ContainValue.equal_range(key);
		for (auto it = itBegin; it != itEnd; ++it)
		{
			if (fnCompare(it->second.Get()))
			{
				return &it->second;
			}
		}

		return nullptr;
	}

	size_t Count(_Key key) const
	{
		return m_ContainValue.count(key);
	}

	void ForEach(std::function<void(_Container&)> fnEach)
	{
		SetDirty();
		for (auto& [_, value] : m_ContainValue)
		{
			fnEach(value.Set());
		}
	}

	bool Erase(_Key key)
	{
		if (m_ContainValue.empty())
			return false;

		auto [itBegin, itEnd] = m_ContainValue.equal_range(key);
		if (itBegin == itEnd)
			return false;

		SetDirty();
		for (auto it = itBegin; it != itEnd; ++it)
		{
			m_PendingRemove.emplace(key, it->second);
			m_ContainValue.erase(it);
		}
		return true;
	}

	bool Erase(_Key key, std::function<bool(const _Container&)> fnCompare)
	{
		if (m_ContainValue.empty())
			return false;

		auto [itBegin, itEnd] = m_ContainValue.equal_range(key);
		if (itBegin == itEnd)
			return false;

		for (auto it = itBegin; it != itEnd; ++it)
		{
			if (fnCompare(it->second.Get()))
			{
				SetDirty();
				m_PendingRemove.emplace(key, it->second);
				m_ContainValue.erase(it);
				return true;
			}
		}
		return false;
	}

	void Clear()
	{
		SetDirty();
		//m_PendingRemove.merge(m_ContainValue);
		for (auto& [key, container] : m_ContainValue)
		{
			m_PendingRemove.emplace(key, std::move(container));
		}
		m_ContainValue.clear();
	}

	void ClearDirty()
	{
		m_isDirty = false;
		m_PendingRemove.clear();
		m_Origin.clear();
	}

	void ClearDirty(_Key key)
	{
		auto [itBegin, itEnd] = m_ContainValue.equal_range(key);

		for (auto it = itBegin; it != itEnd; ++it)
		{
			it->second.ClearDirty();
		}
	}

	void ClearDirty(_Key key, std::function<bool(const _Container&)> fnCompare)
	{
		auto [itBegin, itEnd] = m_ContainValue.equal_range(key);

		for (auto it = itBegin; it != itEnd; ++it)
		{
			if (fnCompare(it->second.Get()))
			{
				it->second.ClearDirty();
				return;
			}
		}
	}

	bool IsDirty() const
	{
		return m_isDirty;
	}

	void Rollback()
	{
		if (!IsDirty())
			return;

		m_PendingRemove.clear();
		if (!m_Origin.empty())
		{
			m_ContainValue.clear();
			m_ContainValue.swap(m_Origin);
		}

		for (auto& it : m_ContainValue | srv::values)
		{
			it.Rollback();
		}
	}

	bool Empty() const
	{
		return m_ContainValue.empty();
	}

private:
	void SetDirty()
	{
		if (!IsDirty())
			m_Origin = m_ContainValue;
		m_isDirty = true;
	}

private:
	bool m_isDirty = false;
	std::multimap<_Key, NSStorageData<_Container>> m_ContainValue;
	std::multimap<_Key, NSStorageData<_Container>> m_Origin;
	std::multimap<_Key, NSStorageData<_Container>> m_PendingRemove;
	static NSStorageData<_Container> nullObject;
};

template <typename _Key, typename _Container>
NSStorageData<_Container> NSStorageDataMultiMap<_Key, _Container>::nullObject = {};
