#include "../stdafx.h"
#include "NSIOCPWorkManager.h"

NSIOCPWorkManager::NSIOCPWorkManager()
{
}

NSIOCPWorkManager::~NSIOCPWorkManager()
{
    Finalize();
}

bool NSIOCPWorkManager::Initialize(int workerCount)
{
    if (m_iocp != nullptr)
        return false;

    // IOCP 생성
    m_iocp = CreateIoCompletionPort(INVALID_HANDLE_VALUE, nullptr, 0, workerCount);
    if (m_iocp == nullptr)
        return false;

    m_workerCount = workerCount;
    m_shutdown = false;

    // 워커 스레드 생성
    m_workerThreads.reserve(workerCount);
    for (int i = 0; i < workerCount; ++i)
    {
        HANDLE thread = CreateThread(
            nullptr,
            0,
            WorkerThread,
            this,
            0,
            nullptr
        );

        if (thread == nullptr)
        {
            Finalize();
            return false;
        }

        m_workerThreads.push_back(thread);
    }

    return true;
}

void NSIOCPWorkManager::Finalize()
{
    if (m_iocp == nullptr)
        return;

    // 종료 플래그 설정
    m_shutdown = true;

    // 모든 워커 스레드에 종료 신호 전송
    for (size_t i = 0; i < m_workerThreads.size(); ++i)
    {
        PostQueuedCompletionStatus(m_iocp, 0, SHUTDOWN_KEY, nullptr);
    }

    // 모든 워커 스레드 종료 대기
    if (!m_workerThreads.empty())
    {
        WaitForMultipleObjects(
            static_cast<DWORD>(m_workerThreads.size()),
            m_workerThreads.data(),
            TRUE,
            INFINITE
        );

        // 스레드 핸들 정리
        for (auto thread : m_workerThreads)
        {
            CloseHandle(thread);
        }
        m_workerThreads.clear();
    }

    // IOCP 핸들 닫기
    if (m_iocp != nullptr)
    {
        CloseHandle(m_iocp);
        m_iocp = nullptr;
    }
}

void NSIOCPWorkManager::PostWork(WorkFunction work)
{
    if (m_iocp == nullptr || m_shutdown)
        return;

    // WorkItem을 unique_ptr로 관리
    auto workItem = std::make_unique<WorkItem>(std::move(work));

    // IOCP에 작업 전달
    if (PostQueuedCompletionStatus(
        m_iocp,
        0,
        WORK_KEY,
        reinterpret_cast<LPOVERLAPPED>(workItem.get())))
    {
        // 성공 시 소유권 해제 (워커 스레드가 삭제 책임)
        workItem.release();
    }
    // 실패 시 unique_ptr가 자동으로 메모리 정리
}

DWORD WINAPI NSIOCPWorkManager::WorkerThread(LPVOID param)
{
    auto* manager = reinterpret_cast<NSIOCPWorkManager*>(param);
    manager->WorkerThreadMain();
    return 0;
}

void NSIOCPWorkManager::WorkerThreadMain()
{
    DWORD bytesTransferred = 0;
    ULONG_PTR completionKey = 0;
    LPOVERLAPPED overlapped = nullptr;

    while (!m_shutdown)
    {
        BOOL result = GetQueuedCompletionStatus(
            m_iocp,
            &bytesTransferred,
            &completionKey,
            &overlapped,
            INFINITE
        );

        // 종료 신호
        if (completionKey == SHUTDOWN_KEY)
        {
            break;
        }

        // 작업 실행
        if (completionKey == WORK_KEY && overlapped != nullptr)
        {
            // unique_ptr로 자동 메모리 관리
            std::unique_ptr<WorkItem> workItem(reinterpret_cast<WorkItem*>(overlapped));
            
            try
            {
                if (workItem->function)
                {
                    workItem->function();
                }
            }
            catch (...)
            {
                // 예외 처리 - 로깅 등
            }
            // unique_ptr가 scope 종료 시 자동으로 메모리 정리
        }
    }
}