#pragma once
#include "NSDefine.h"
// #include "NSSingleton.h" // 게임서버에서 제공
#include "DBPromise.h"
#include "mimalloc_integration.h"
#include "ThreadSafetyAnnotations.h"
#include <memory>
#include <type_traits>
#include <atomic>
#include <functional>
#include <vector>
#include <optional>
#include <source_location>
#include <queue>
#include <unordered_map>
#include <shared_mutex>
#include <thread>
#include <condition_variable>

// Forward declaration for EDataBase enum (defined elsewhere)
enum class EDataBase;
enum class EDBProvider;

// Forward declarations
class NSDataSerializer;
class NSQueryData;
class NSStoredProcedure;
class NSMySQLConnectionPool;
class NSMySQLConnection;
struct NSStorageUpdateContainer;

// Internal components forward declarations
namespace Database
{
    class CIDQueueManager;
    class WorkerThreadPool;
    class AsyncQueryPoller;
}

// Connection은 게임서버에서 정의됨
struct Connection;

// Database namespace
namespace Database
{
    class AsyncQueryExecutor;
    struct AsyncQueryTask;
    
    // 쿼리 작업 구조체
    struct QueryTask
    {
        EDataBase dbType;  // Connection 대신 DB 타입 직접 사용
        NSDataSerializer serializer;
        DBPromise<std::shared_ptr<NSQueryData>> promise;
        std::shared_ptr<NSQueryData> queryData;
        std::function<void(const std::shared_ptr<NSQueryData>&)> afterExecuteCallback;
        std::string procedureName;
        int64_t cid;
        std::function<void(NSMySQLConnection*, const NSDataSerializer&, std::shared_ptr<NSQueryData>)> executeFunc;
    };
    
    // 쿼리 타이머 (RAII)
    class QueryTimer
    {
    public:
        QueryTimer(std::shared_ptr<NSQueryData> data, 
                   std::function<void(const std::shared_ptr<NSQueryData>&)> callback);
        ~QueryTimer();
        
        QueryTimer(const QueryTimer&) = delete;
        QueryTimer& operator=(const QueryTimer&) = delete;
        QueryTimer(QueryTimer&&) = delete;
        QueryTimer& operator=(QueryTimer&&) = delete;
        
    private:
        std::chrono::high_resolution_clock::time_point m_start;
        std::shared_ptr<NSQueryData> m_queryData;
        std::function<void(const std::shared_ptr<NSQueryData>&)> m_callback;
    };
}

// 심플하고 레거시 호환되는 데이터베이스 매니저
class NSDataBaseManager : public TemplateSingleton<NSDataBaseManager>
{
public:
    NSDataBaseManager();
    ~NSDataBaseManager();

public:
    // 초기화
    bool Initialize();
    void Finalize();
    
    // 워커 스레드 관리
    bool Start(uint32_t workThreadCnt = 0);  // 0이면 자동 계산
    void Stop();
    void ProhibitPushAccess();
    void StopAllWorkerThreadAndWait();
    
    // 레거시 호환 인터페이스
    bool AddConnectionInfo(EDataBase dbType, EDBProvider provider, const std::string_view host, 
                          uint32_t port, const std::string_view dbName, const std::string_view user, 
                          const std::string_view password, int32_t initPoolCount);
    NSMySQLConnectionPool* GetDBConnection(int32 dbType);
    void ReconnectConnection(int32 dbType);
    
    // 모니터링
    uint32_t GetDBThreadCount() const { return m_threadCount.value_or(0); }
    int64_t GetQueriesProcessingCount() const { 
        return m_asyncQueryPoller ? static_cast<int64_t>(m_asyncQueryPoller->GetActiveQueryCount()) : 0;
    }
    int64_t GetDBQueueSize() const;
    std::string GetConnectionPoolCountInfo() const;
    std::string GetConnectionPoolCountLog() const;
    
    // IO 카운트
    int64_t GetInputCount() const { return m_inputCount.load(); }
    int64_t GetOutputCount() const { return m_outputCount.load(); }
    void ResetIOCount() { m_inputCount = 0; m_outputCount = 0; }
    
    // 지연된 작업 실행
    void PostDelayedTask(int delayMs, std::function<void()> task);
    
    // 워커 풀에 작업 전달
    void PostWork(std::function<void()> work);
    
    // 콜백 설정
    void SetAfterExecuteQuery(std::function<void(const NSQueryData&)> callback) { m_afterExecuteQuery = callback; }
    void SetAfterExecuteQuery(std::function<void(const std::shared_ptr<NSQueryData>&)> callback) { m_afterExecuteQueryShared = callback; }
    
    // 게임 스레드 디스패처 설정
    void SetGameThreadDispatcher(std::function<void(std::function<void()>)> dispatcher) 
    { 
        m_gameThreadPost = dispatcher;
        GameThreadCallback::SetGameThreadDispatcher(dispatcher);
    }

    // StartQuery 오버로드들 (레거시 호환)
    
    // 1. DataSerializer와 샤드키 받는 버전 (레거시)
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        NSDataSerializer& dataSerializer,
        uint64_t shardKey = 0)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");

        // SP에서 DB 타입 자동 획득
        SP sp;
        EDataBase dbType = sp.GetDBType();
        
        return StartQueryImpl<SP>(dbType, serializer);
    }
    
    // 2. 세션만 받는 버전 (레거시)
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        std::shared_ptr<class NSDBSession> session)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");
            
        NSDataSerializer serializer;
        
        // SP에서 DB 타입 자동 획득
        SP sp;
        EDataBase dbType = sp.GetDBType();
        
        return StartQueryImpl<SP>(dbType, serializer);
    }
    
    // 3. 세션과 DataSerializer 받는 버전 (레거시)
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        std::shared_ptr<class NSDBSession> session,
        NSDataSerializer& dataSerializer)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");
            
        // SP에서 DB 타입 자동 획득
        SP sp;
        EDataBase dbType = sp.GetDBType();
        
        return StartQueryImpl<SP>(dbType, dataSerializer);
    }

    // Cid 기반 연결
    Connection GetConnectionByCid(int32 cid)
    {
        return Connection(static_cast<int32>(EDataBase::Game));
    }

    // Aid 기반 연결
    Connection GetConnectionByAid(int32 aid)
    {
        return Connection(static_cast<int32>(EDataBase::Game));
    }

    // CommonDB 연결
    Connection GetCommonDBConnection()
    {
        return Connection(static_cast<int32>(EDataBase::Common));
    }

    // LogDB 연결
    Connection GetLogDBConnection()
    {
        return Connection(static_cast<int32>(EDataBase::Log));
    }
    
    // Storage Update 관련 메서드 (레거시 호환)
    DBPromise<std::shared_ptr<NSQueryData>> StorageUpdateQuery(
        std::shared_ptr<NSStorageUpdateContainer> containerData,
        std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pQueryFunc,
        std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
        std::shared_ptr<NSDBSession> session = nullptr,
        std::source_location location = std::source_location::current()
    );

    template <typename Sp>
    DBPromise<std::shared_ptr<NSQueryData>> StorageUpdateQueryWithCustomProcedure(
        std::shared_ptr<NSStorageUpdateContainer> containerData,
        NSDataSerializer& dataSerializer,
        std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
        std::shared_ptr<NSDBSession> session = nullptr,
        std::source_location location = std::source_location::current()
    )
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
            auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);
            queryData->GetQueryData() = dataSerializer;

            // CID 기반 스레드 선택
            int threadIndex = containerData->Cid % m_threadCount.value_or(1);
            
            // 작업 큐에 추가
            m_workerManager->QueueWork([=]() mutable {
                try {
                    // SP 타입의 프로시저 실행
                    auto* pool = GetConnectionPool(static_cast<int32>(EDataBase::Game));
                    if (!pool) {
                        promise.SetException(std::make_exception_ptr(
                            std::runtime_error("Connection pool not found")));
                        return;
                    }
                    
                    auto connection = pool->GetConnection();
                    if (!connection) {
                        promise.SetException(std::make_exception_ptr(
                            std::runtime_error("Failed to get connection")));
                        return;
                    }
                    
                    Sp procedure;
                    auto result = procedure.Execute(connection.get(), queryData->GetQueryData());
                    queryData->SetErrorCode(result);
                    
                    // 결과 처리
                    if (pResultFunc) {
                        pResultFunc(queryData, containerData);
                    }
                    
                    promise.SetValue(queryData);
                    pool->ReturnConnection(std::move(connection));
                    
                } catch (...) {
                    promise.SetException(std::current_exception());
                }
            }, threadIndex);
        });
    }


private:
    // 실제 구현
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQueryImpl(
        EDataBase dbType,
        const NSDataSerializer& serializer,
        int64_t transactionId = 0);

    // 연결 풀 가져오기
    NSMySQLConnectionPool* GetConnectionPool(int32 dbType);
    
    // 샤드키 기반 스레드 선택
    int GetExecutorByShardKey(uint64_t shardKey) const {
        return shardKey % m_threadCount.value_or(1);
    }

    // 내부 컴포넌트와의 연동 메서드
    void EnqueueQuery(int64_t cid, Database::QueryTask task);
    void ProcessCIDQueue(int64_t cid);
    void ProcessCIDQueueWithConnection(int64_t cid, std::shared_ptr<NSMySQLConnection> conn);
    void StartAsyncQuery(const Database::QueryTask& task);
    void ExecuteQueryWithConnection(const Database::QueryTask& task, std::shared_ptr<NSMySQLConnection> conn);
    void ProcessCompletedQuery(Database::AsyncQueryTask& asyncTask);
    void CheckNextTaskForCID(int64_t cid);
    void PostToGameThread(int64_t cid, std::shared_ptr<NSQueryData> queryData);

private:
    // 연결 풀 배열 (EDataBase::End 크기)
    std::unique_ptr<NSMySQLConnectionPool> m_connectionPools[static_cast<int>(EDataBase::End)];
    
    // 내부 컴포넌트들
    std::unique_ptr<Database::CIDQueueManager> m_cidQueueManager;
    std::unique_ptr<Database::WorkerThreadPool> m_workerThreadPool;
    std::unique_ptr<Database::AsyncQueryPoller> m_asyncQueryPoller;
    
    // 스레드 수
    std::optional<uint32_t> m_threadCount;
    
    // 통계
    std::atomic<int64_t> m_queriesProcessing{0};
    std::atomic<int64_t> m_inputCount{0};
    std::atomic<int64_t> m_outputCount{0};
    
    // 콜백
    std::function<void(const NSQueryData&)> m_afterExecuteQuery;
    std::function<void(const std::shared_ptr<NSQueryData>&)> m_afterExecuteQueryShared;
    
    // 게임 스레드 콜백 (GameThreadCallback 통합)
    using GameThreadPostFunc = std::function<void(std::function<void()>)>;
    GameThreadPostFunc m_gameThreadPost;
    
    // 상태
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_pushAccessProhibit{false};
    std::atomic<int32_t> m_pushAccessCount{0};
    std::atomic<bool> m_isShuttingDown{false};
    
    // 시퀀스 관리 - 샤드 방식으로 경쟁 최소화
    static constexpr size_t SEQUENCE_SHARD_COUNT = 256;  // 최적 성능을 위한 샤드 수
    struct alignas(64) SequenceShard {  // 캐시라인 정렬로 false sharing 방지
        mutable std::mutex mutex;
        std::unordered_map<int64_t, int64_t> sequences;
    };
    mutable SequenceShard m_sequenceShards[SEQUENCE_SHARD_COUNT];
    
    // 샤드 인덱스 계산 - 비트 마스크로 최적화
    size_t GetSequenceShardIndex(int64_t cid) const {
        return std::hash<int64_t>{}(cid) & (SEQUENCE_SHARD_COUNT - 1);  // % 256 = & 0xFF
    }
    
public:
    // 시퀀스 관리 메서드
    int64_t GetNextStorageSequence(int64_t cid);
    
    // 프로시저 메타데이터 전역 관리
    bool LoadAllProcedureMetadata();
    std::shared_ptr<NSMySQLConnection::ProcedureMetadata> GetGlobalProcedureMetadata(const std::string& procName);
    
private:
    // 전역 프로시저 메타데이터 캐시 (모든 DB 스키마 공유)
    static mi::unordered_map<std::string, std::shared_ptr<NSMySQLConnection::ProcedureMetadata>> s_globalMetadataCache;
    static std::shared_mutex s_globalMetadataMutex;
};