#pragma once

// mimalloc 통합 - 항상 사용
#include <mimalloc.h>
#include <string>
#include <vector>
#include <deque>
#include <queue>
#include <list>
#include <unordered_map>
#include <unordered_set>

// 전역 new/delete 오버라이드 제거
// 필요한 경우 아래의 MimallocAllocator를 사용하여 명시적으로 mimalloc 사용

// mimalloc STL 사용 예시:
// using mi_string = std::basic_string<char, std::char_traits<char>, MimallocAllocator<char>>;
// using mi_vector = std::vector<T, MimallocAllocator<T>>;
// using mi_map = std::unordered_map<K, V, std::hash<K>, std::equal_to<K>, 
//                                   MimallocAllocator<std::pair<const K, V>>>;

// STL 컨테이너용 커스텀 할당자
template<typename T>
class MimallocAllocator 
{
public:
    using value_type = T;
    using size_type = std::size_t;
    using difference_type = std::ptrdiff_t;

    MimallocAllocator() = default;

    template<typename U>
    MimallocAllocator(const MimallocAllocator<U>&) noexcept {}

    T* allocate(size_type n) 
    {
        return static_cast<T*>(mi_malloc(n * sizeof(T)));
    }

    void deallocate(T* ptr, size_type) noexcept 
    {
        mi_free(ptr);
    }

    template<typename U>
    bool operator==(const MimallocAllocator<U>&) const noexcept 
    {
        return true;
    }

    template<typename U>
    bool operator!=(const MimallocAllocator<U>&) const noexcept 
    {
        return false;
    }
};

// mimalloc 사용하는 STL 컨테이너 타입 정의
// 필요한 경우에만 명시적으로 사용
namespace mi {
    // 문자열
    using string = std::basic_string<char, std::char_traits<char>, MimallocAllocator<char>>;
    using wstring = std::basic_string<wchar_t, std::char_traits<wchar_t>, MimallocAllocator<wchar_t>>;
    
    // 컨테이너
    template<typename T>
    using vector = std::vector<T, MimallocAllocator<T>>;
    
    template<typename T>
    using deque = std::deque<T, MimallocAllocator<T>>;
    
    template<typename T>
    using list = std::list<T, MimallocAllocator<T>>;
    
    template<typename T>
    using queue = std::queue<T, std::deque<T, MimallocAllocator<T>>>;
    
    template<typename K, typename V, typename Hash = std::hash<K>, typename Pred = std::equal_to<K>>
    using unordered_map = std::unordered_map<K, V, Hash, Pred, MimallocAllocator<std::pair<const K, V>>>;
    
    template<typename K, typename Hash = std::hash<K>, typename Pred = std::equal_to<K>>
    using unordered_set = std::unordered_set<K, Hash, Pred, MimallocAllocator<K>>;
    
    // unique_ptr with custom deleter for mimalloc
    template<typename T>
    using unique_ptr = std::unique_ptr<T, decltype(&mi_free)>;
    
    // make_unique for array types with mimalloc
    template<typename T>
    auto make_unique_array(size_t n) {
        return unique_ptr<T[]>(
            static_cast<T*>(mi_malloc(n * sizeof(T))), 
            &mi_free
        );
    }
    
    // 사용 예시:
    // mi::string myString = "Hello";
    // mi::vector<int> myVector;
    // mi::queue<int> myQueue;
    // mi::unordered_map<int, std::string> myMap;
    // mi::unique_ptr<char[]> buffer = mi::make_unique_array<char>(1024);
}