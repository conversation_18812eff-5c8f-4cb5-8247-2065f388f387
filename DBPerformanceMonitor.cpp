#include "DBPerformanceMonitor.h"
#include "Diagnostics/NSLogger.h"
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <regex>

namespace Database
{

DBPerformanceMonitor& DBPerformanceMonitor::GetInstance()
{
    static DBPerformanceMonitor instance;
    return instance;
}

void DBPerformanceMonitor::RecordQueryStart(int64_t cid, const std::string& query)
{
    m_totalQueries.fetch_add(1);
    
    std::string procedureName = ExtractProcedureName(query);
    
    auto activeQuery = std::make_unique<ActiveQuery>();
    activeQuery->procedureName = procedureName;
    activeQuery->startTime = std::chrono::steady_clock::now();
    
    std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
    m_activeQueries[cid] = std::move(activeQuery);
}

void DBPerformanceMonitor::RecordQueryComplete(int64_t cid, bool success)
{
    std::unique_ptr<ActiveQuery> activeQuery;
    
    {
        std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
        
        auto it = m_activeQueries.find(cid);
        if (it == m_activeQueries.end())
            return;
        
        activeQuery = std::move(it->second);
        m_activeQueries.erase(it);
    }
    
    auto elapsed = std::chrono::steady_clock::now() - activeQuery->startTime;
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count();
    
    // 전역 통계 업데이트
    if (success)
    {
        m_successfulQueries.fetch_add(1);
    }
    else
    {
        m_failedQueries.fetch_add(1);
    }
    m_totalTimeMs.fetch_add(elapsedMs);
    
    // 프로시저별 통계 업데이트
    UpdateStatistics(activeQuery->procedureName, elapsedMs, success);
}

void DBPerformanceMonitor::LogStatistics()
{
    auto now = std::chrono::steady_clock::now();
    m_lastLogTime = now;
    
    std::stringstream ss;
    ss << "\n========== DB Performance Statistics ==========\n";
    
    // 전역 통계
    auto globalStats = GetGlobalStatistics();
    ss << "=== Global Statistics ===\n"
       << "Total Queries: " << globalStats.totalQueries << "\n"
       << "Successful: " << globalStats.successfulQueries << "\n"
       << "Failed: " << globalStats.failedQueries << "\n";
    
    if (globalStats.totalQueries > 0)
    {
        double successRate = (static_cast<double>(globalStats.successfulQueries) / globalStats.totalQueries) * 100.0;
        ss << "Success Rate: " << std::fixed << std::setprecision(2) << successRate << "%\n";
    }
    
    ss << "Total Time: " << globalStats.totalTimeMs << " ms\n"
       << "Average Time: " << std::fixed << std::setprecision(2) << globalStats.avgTimeMs << " ms\n"
       << "Active Queries: " << globalStats.activeQueries << "\n\n";
    
    // 프로시저별 통계
    std::unordered_map<std::string, Statistics> procedureStats;
    GetProcedureStatistics(procedureStats);
    
    if (!procedureStats.empty())
    {
        ss << "=== Procedure Statistics ===\n";
        
        // 실행 횟수 기준으로 정렬
        std::vector<std::pair<std::string, Statistics>> sortedStats(procedureStats.begin(), procedureStats.end());
        std::sort(sortedStats.begin(), sortedStats.end(), 
                  [](const auto& a, const auto& b) { return a.second.totalQueries > b.second.totalQueries; });
        
        for (const auto& [procName, stats] : sortedStats)
        {
            ss << "\nProcedure: " << procName << "\n"
               << "  Executions: " << stats.totalQueries << "\n"
               << "  Success: " << stats.successfulQueries << "\n"
               << "  Failed: " << stats.failedQueries << "\n";
            
            if (stats.totalQueries > 0)
            {
                double failureRate = (static_cast<double>(stats.failedQueries) / stats.totalQueries) * 100.0;
                ss << "  Failure Rate: " << std::fixed << std::setprecision(2) << failureRate << "%\n";
            }
            
            ss << "  Total Time: " << stats.totalTimeMs << " ms\n"
               << "  Avg Time: " << std::fixed << std::setprecision(2) << stats.avgTimeMs << " ms\n"
               << "  Min Time: " << (stats.minTimeMs == UINT64_MAX ? 0 : stats.minTimeMs) << " ms\n"
               << "  Max Time: " << stats.maxTimeMs << " ms\n";
        }
    }
    
    ss << "\n==============================================";
    LOGI << ss.str();
}

void DBPerformanceMonitor::ResetStatistics()
{
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_queryStats.clear();
    }
    
    m_totalQueries.store(0);
    m_successfulQueries.store(0);
    m_failedQueries.store(0);
    m_totalTimeMs.store(0);
    
    LOGI << "DB Performance statistics reset";
}

DBPerformanceMonitor::Statistics DBPerformanceMonitor::GetGlobalStatistics() const
{
    Statistics stats;
    stats.totalQueries = m_totalQueries.load();
    stats.successfulQueries = m_successfulQueries.load();
    stats.failedQueries = m_failedQueries.load();
    stats.totalTimeMs = m_totalTimeMs.load();
    
    if (stats.totalQueries > 0)
    {
        stats.avgTimeMs = static_cast<double>(stats.totalTimeMs) / stats.totalQueries;
    }
    
    {
        std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
        stats.activeQueries = m_activeQueries.size();
    }
    
    return stats;
}

void DBPerformanceMonitor::GetProcedureStatistics(std::unordered_map<std::string, Statistics>& stats) const
{
    stats.clear();
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    for (const auto& [procName, queryStats] : m_queryStats)
    {
        Statistics procStats;
        procStats.totalQueries = queryStats->count.load();
        procStats.failedQueries = queryStats->failures.load();
        procStats.successfulQueries = procStats.totalQueries - procStats.failedQueries;
        procStats.totalTimeMs = queryStats->totalTimeMs.load();
        procStats.minTimeMs = queryStats->minTimeMs.load();
        procStats.maxTimeMs = queryStats->maxTimeMs.load();
        
        if (procStats.totalQueries > 0)
        {
            procStats.avgTimeMs = static_cast<double>(procStats.totalTimeMs) / procStats.totalQueries;
        }
        
        stats[procName] = procStats;
    }
}

std::string DBPerformanceMonitor::ExtractProcedureName(const std::string& query) const
{
    // CALL 프로시저명(...) 패턴에서 프로시저명 추출
    static const std::regex callPattern(R"(CALL\s+([^\s(]+))", std::regex::icase);
    
    std::smatch match;
    if (std::regex_search(query, match, callPattern) && match.size() > 1)
    {
        return match[1].str();
    }
    
    // SELECT/INSERT/UPDATE/DELETE 등의 기본 쿼리 타입 추출
    static const std::regex queryTypePattern(R"(^\s*(SELECT|INSERT|UPDATE|DELETE|CALL))", std::regex::icase);
    
    if (std::regex_search(query, match, queryTypePattern) && match.size() > 1)
    {
        return match[1].str();
    }
    
    // 추출 실패 시 쿼리의 첫 20자 반환
    return query.substr(0, std::min(size_t(20), query.length())) + "...";
}

void DBPerformanceMonitor::UpdateStatistics(const std::string& procedureName, uint64_t elapsedMs, bool success)
{
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    auto it = m_queryStats.find(procedureName);
    if (it == m_queryStats.end())
    {
        auto stats = std::make_unique<QueryStats>();
        it = m_queryStats.emplace(procedureName, std::move(stats)).first;
    }
    
    auto& stats = it->second;
    stats->count.fetch_add(1);
    stats->totalTimeMs.fetch_add(elapsedMs);
    
    if (!success)
    {
        stats->failures.fetch_add(1);
    }
    
    // 최소/최대 시간 업데이트
    uint64_t currentMin = stats->minTimeMs.load();
    while (elapsedMs < currentMin && !stats->minTimeMs.compare_exchange_weak(currentMin, elapsedMs))
    {
        // 재시도
    }
    
    uint64_t currentMax = stats->maxTimeMs.load();
    while (elapsedMs > currentMax && !stats->maxTimeMs.compare_exchange_weak(currentMax, elapsedMs))
    {
        // 재시도
    }
}

} // namespace Database