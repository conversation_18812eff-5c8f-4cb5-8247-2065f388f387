#pragma once

// Database_Maria 전용 moodycamel 네임스페이스 격리
// ODR(One Definition Rule) 위반 방지를 위해 별도 네임스페이스 사용
namespace db_internal {
    #include "concurrentqueue.h"
    #include "blockingconcurrentqueue.h"
}

// 타입 별칭으로 사용 편의성 제공
namespace Database {
    template<typename T>
    using ConcurrentQueue = db_internal::moodycamel::ConcurrentQueue<T>;
    
    template<typename T>
    using BlockingConcurrentQueue = db_internal::moodycamel::BlockingConcurrentQueue<T>;
}