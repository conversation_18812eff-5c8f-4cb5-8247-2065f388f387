#include "stdafx.h"
#include "NSDefineEnum.h"

#include "NSSQLServerConnectionPool.h"

#include "ADO/NSAdoRecordset.h"
#include "ADO/NSAdoCommand.h"

#include "Formatter/AdoEnums.h"

NSSQLServerConnectionPool::NSSQLServerConnectionPool(const std::string_view host, uint32_t port, const std::string_view dbName,
	const std::string_view user, const std::string_view password)
	: NSConnectionPool(host, port, dbName, user, password)
{
}

NSAdoConnection* NSSQLServerConnectionPool::Alloc()
{
	std::ostringstream strConnection_info;
	strConnection_info << "Provider=MSOLEDBSQL;";
	strConnection_info << "Network Library=DBMSSOCN;";
	strConnection_info << "Data Source=" << m_Host << "," << m_Port << ";";
	strConnection_info << "Initial Catalog=" << m_DBName << ";";
	strConnection_info << "Auto Translate=FALSE;";
	strConnection_info << "User ID=" << m_UserID << ";";
	strConnection_info << "Password=" << m_Password << ";";

	NSAdoConnection* connection = new NSAdoConnection();
	if (!connection->Connect(strConnection_info.str().c_str()))
	{
		LOGE << std::format("Connection Fail : {}", strConnection_info.str());
		delete connection;
		return nullptr;
	}

	m_vtAlloc.push_back(connection);

	//ADODB COMMAND 셋팅
	std::unique_ptr<NSAdoRecordset> spList = connection->ExecuteQuery("SELECT SPECIFIC_NAME FROM information_schema.routines where routine_type = 'PROCEDURE'");
	if (spList)
	{
		char			strSpName[256] = { 0 };
		char			strSpNameQuery[1024] = { 0 };
		int				ixType = 0;
		int				iColKey = 0;
		int				iColLen = 0;
		char			strColName[256] = { 0 };
		const char* spParameter = R"(
			SELECT 
			syscolumns.name AS ColName, 
			syscolumns.colorder AS ColOrder, 
			syscolumns.length AS ColLen, 
			syscolumns.colstat AS ColKey, 
			systypes.xtype 
			FROM 
			dbo.syscolumns INNER JOIN 
			dbo.sysobjects ON syscolumns.id = sysobjects.id INNER JOIN 
			dbo.systypes ON syscolumns.xtype = systypes.xtype 
			WHERE
			(sysobjects.name = '%s') 
			AND 
			(systypes.status <> 1) 
			ORDER BY 
			sysobjects.name, 
			syscolumns.colorder
		)";

		while (!spList->IsEOF())
		{
			//파라미터 목록을 가져온다.
			memset(strSpName, 0, sizeof(strSpName));
			memset(strSpNameQuery, 0, sizeof(strSpNameQuery));
			spList->GetItem("SPECIFIC_NAME", strSpName, sizeof(strSpName));
			sprintf_s(strSpNameQuery, sizeof(strSpNameQuery), spParameter, strSpName);

			std::string szSPName(strSpName);
			if (szSPName.substr(0, 2).compare("gm") == 0)
				continue;

			std::unique_ptr<NSAdoRecordset> spParameterList = connection->ExecuteQuery(strSpNameQuery);
			if (spParameterList)
			{
				//커맨드 생성
				NSAdoCommand* pcCmd = new NSAdoCommand();
				pcCmd->SetProcedure(connection->GetConnection(), strSpName, 10, true);
				//리턴값은 무조건 넣어주자.
				pcCmd->SetParameter("@ReturnValue", adInteger, adParamReturnValue, 4);
				while (!spParameterList->IsEOF())
				{
					memset(strColName, 0, sizeof(strColName));
					spParameterList->GetItem("ColName", strColName, sizeof(strColName));
					spParameterList->GetItem("ColLen", iColLen);
					spParameterList->GetItem("ColKey", iColKey);
					spParameterList->GetItem("xtype", ixType);
					pcCmd->SetParameter(strColName, GetDataTypeEnum(ixType), GetParameterDirectionEnum(iColKey), iColLen);
				}
				connection->AddCommand(strSpName, pcCmd);

				spParameterList->Close();
			}
		}

		spList->Close();
	}

	LOGI << std::format("Init Database Connection {}", m_DBName);

	_ConnectionPtr conn = connection->GetConnection();
	if (conn != nullptr)
	{
		LOGI << std::format("+ Version : {}", static_cast<const char*>(conn->GetVersion()));
		LOGI << std::format("+ Connection State : {}", static_cast<ObjectStateEnum>(conn->GetState()));

		PropertiesPtr properties = conn->GetProperties();
		if (properties != nullptr)
		{
			for (std::string name : {
				"Provider Name", "DBMS Name", "DBMS Version",
					"Multiple Parameter Sets", "Multiple Results"
			})
			{
				PropertyPtr property = properties->GetItem(name.c_str());
				if (property == nullptr)
					continue;

				LOGI << std::format("+ {} : {}",
					static_cast<const char*>(property->GetName()), static_cast<const char*>(_bstr_t { property->GetValue() }));
			}
		}
	}

	return connection;
}

DataTypeEnum NSSQLServerConnectionPool::GetDataTypeEnum(int type)
{
	DataTypeEnum enDataType = DataTypeEnum::adEmpty;

	switch (type)
	{
	case 34:
		enDataType = adBinary;
		break;
	case 35:
		enDataType = adLongVarChar;
		break;
	case 40:
		enDataType = adDate;
		break;
	case 41:
		enDataType = adDBTime;
		break;
	case 48:
		enDataType = adTinyInt;
		break;
	case 52:
		enDataType = adSmallInt;
		break;
	case 56:
		enDataType = adInteger;
		break;
	case 58:
		enDataType = adDBTimeStamp;
		break;
	case 59:
		enDataType = adSingle;
		break;
	case 60:
		enDataType = adCurrency;
		break;
	case 61:
		enDataType = adDBTimeStamp;
		break;
	case 62:
		enDataType = adDouble;
		break;
	case 99:
		enDataType = adLongVarWChar;
		break;
	case 104:
		enDataType = adBoolean;
		break;
	case 106:
		enDataType = adCurrency;
		break;
	case 108:
		enDataType = adNumeric;
		break;
	case 122:
		enDataType = adCurrency;
		break;
	case 127:
		enDataType = adBigInt;
		break;
	case 165:
		enDataType = adVarBinary;
		break;
	case 167:
		enDataType = adVarChar;
		break;
	case 173:
		enDataType = adBinary;
		break;
	case 175:
		enDataType = adChar;
		break;
	case 231:
		enDataType = adVarWChar;
		break;
	case 239:
		enDataType = adWChar;
		break;
	case 241:
		enDataType = adChar;
		break;
	default:
		LOGE << std::format("CHECK DATABASE TYPE :{}", type);
		break;
	}

	return enDataType;
}

ParameterDirectionEnum NSSQLServerConnectionPool::GetParameterDirectionEnum(int type)
{
	ParameterDirectionEnum enDir = ParameterDirectionEnum::adParamUnknown;
	switch (type)
	{
	case 0:
		enDir = adParamInput;
		break;
	case 4:
		enDir = adParamOutput;
		break;
	default:
		LOGE << std::format("CHECK DATABASE TYPE :{}", type);
		break;
	}

	return enDir;
}
