#include "../stdafx.h"
#include "NSMySQLConnectionPool.h"
#include "../Connection/NSMySQLConnection.h"
#include "../NSDataBaseManager.h"
#include "../Diagnostics/NSLogger.h"
#include <algorithm>
#include <condition_variable>
#include <queue>

NSMySQLConnectionPool::NSMySQLConnectionPool()
{
}

NSMySQLConnectionPool::NSMySQLConnectionPool(const std::string& host, int port, const std::string& dbName,
                                             const std::string& user, const std::string& password)
    : m_host(host)
    , m_port(port)
    , m_database(dbName)
    , m_user(user)
    , m_password(password)
{
}

NSMySQLConnectionPool::~NSMySQLConnectionPool()
{
    Finalize();
}

bool NSMySQLConnectionPool::Initialize(int databaseType, int shardId)
{
    bool expected = false;
    if (!m_initialized.compare_exchange_strong(expected, true))
        return true; // Already initialized

    m_databaseType = databaseType;
    m_shardId = shardId;

    // Load connection info if not set
    if (m_host.empty())
    {
        if (!LoadConnectionInfo())
        {
            m_initialized = false;
            return false;
        }
    }

    // Create connections WITHOUT holding the mutex
    std::vector<std::unique_ptr<ConnectionEntry>> tempConnections;
    tempConnections.reserve(m_maxConnections);
    
    LOGI << "Creating " << m_maxConnections << " connections for pool " 
         << databaseType << ":" << shardId;
    
    for (int i = 0; i < m_maxConnections; ++i)
    {
        // Network operation without mutex
        auto conn = CreateConnection();
        if (conn)
        {
            auto entry = std::make_unique<ConnectionEntry>();
            entry->connection = conn;
            entry->inUse = false;
            tempConnections.push_back(std::move(entry));
        }
        else
        {
            LOGE << "Failed to create connection " << i << " for pool " 
                 << databaseType << ":" << shardId;
            
            // Clean up already created connections
            for (auto& entry : tempConnections)
            {
                if (entry && entry->connection) 
                    entry->connection->Disconnect();
            }
            m_initialized = false;
            return false;
        }
    }

    // Now quickly move all connections under mutex
    {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        m_connections = std::move(tempConnections);
    }

    LOGI << "ConnectionPool initialized: type=" << databaseType 
         << " shard=" << shardId 
         << " connections=" << m_connections.size();
         
    return true;
}

void NSMySQLConnectionPool::Finalize()
{
    bool expected = true;
    if (!m_initialized.compare_exchange_strong(expected, false))
        return; // Already finalized

    m_shutting_down = true;

    // Clear waiting tasks
    WaitingTask task;
    while (m_waitingTasks.try_dequeue(task))
    {
        // Just dequeue all tasks to clear the queue
    }

    // Close all connections
    std::lock_guard<std::mutex> lock(m_connectionsMutex);
    for (auto& entry : m_connections)
    {
        if (entry && entry->connection)
        {
            entry->connection->Disconnect();
        }
    }
    m_connections.clear();

    LOGI << "ConnectionPool finalized";
}

std::shared_ptr<NSMySQLConnection> NSMySQLConnectionPool::GetConnection()
{
    if (!m_initialized.load() || m_shutting_down.load())
        return nullptr;

    std::lock_guard<std::mutex> lock(m_connectionsMutex);
    
    // 사용 가능한 연결 찾기 (즉시 반환)
    for (auto& entry : m_connections)
    {
        if (entry && entry->connection)
        {
            bool expected = false;
            // 먼저 사용 중 플래그를 체크하고 설정
            if (entry->inUse.compare_exchange_strong(expected, true))
            {
                // 연결 상태 확인
                if (entry->connection->IsConnected())
                {
                    // 성공적으로 획득
                    return entry->connection;
                }
                else
                {
                    // 연결이 끊어졌으면 플래그 복원
                    entry->inUse = false;
                }
            }
        }
    }
    
    // 사용 가능한 연결 없음
    return nullptr;
}

void NSMySQLConnectionPool::ReturnConnection(std::shared_ptr<NSMySQLConnection> conn)
{
    if (!conn)
        return;
    
    // 대기 중인 작업 확인
    WaitingTask waitingTask;
    bool hasWaiting = m_waitingTasks.try_dequeue(waitingTask);
    
    if (hasWaiting)
    {
        LOGD << "Found waiting task for CID: " << waitingTask.cid 
             << ", remaining: " << m_waitingTasks.size_approx();
    }
    
    if (hasWaiting)
    {
        // 대기 작업에 커넥션 직접 전달
        // 콜백을 직접 호출 (이미 워커 스레드에서 실행 중)
        waitingTask.callback(conn);
        return;
    }
    
    // 대기 작업이 없으면 일반 반환
    {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        
        for (auto& entry : m_connections)
        {
            if (entry && entry->connection == conn)
            {
                entry->inUse = false;
                break;
            }
        }
    }
    
    // 락 밖에서 알림 (데드락 방지)
    m_connectionAvailable.notify_one();
}

void NSMySQLConnectionPool::RegisterWaitingTask(int64_t cid, 
    std::function<void(std::shared_ptr<NSMySQLConnection>)> callback)
{
    // 셧다운 중이면 태스크 거부
    if (m_shutting_down.load())
    {
        LOGW << "Connection pool is shutting down, rejecting task for CID: " << cid;
        return;
    }
    
    // 대기 큐가 너무 크면 거부
    size_t approxSize = m_waitingTasks.size_approx();
    if (approxSize >= 1000)
    {
        LOGW << "Too many waiting tasks (" << approxSize << "), rejecting CID: " << cid;
        return;
    }
    
    m_waitingTasks.enqueue({cid, callback});
    LOGD << "Registered waiting task for CID: " << cid << ", queue size: " << m_waitingTasks.size_approx();
}

Database::ConnectionInfo NSMySQLConnectionPool::GetConnectionInfo() const
{
    return Database::ConnectionInfo(m_host, m_port, m_user, m_password, m_database);
}

bool NSMySQLConnectionPool::AddConnectionInfo(const std::string& host, int port, 
                                             const std::string& dbName, 
                                             const std::string& user, 
                                             const std::string& password)
{
    if (m_initialized.load())
    {
        LOGW << "Cannot change connection info after initialization";
        return false;
    }

    m_host = host;
    m_port = port;
    m_database = dbName;
    m_user = user;
    m_password = password;
    
    return true;
}

void NSMySQLConnectionPool::Reconnect()
{
    if (!m_initialized.load())
        return;

    // Collect disconnected connections without holding mutex
    std::vector<std::pair<size_t, std::shared_ptr<NSMySQLConnection>>> toReconnect;
    
    {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        for (size_t i = 0; i < m_connections.size(); ++i)
        {
            auto& entry = m_connections[i];
            if (entry && entry->connection && !entry->connection->IsConnected())
            {
                toReconnect.push_back({i, entry->connection});
            }
        }
    }
    
    // Reconnect without holding mutex
    for (auto& [index, conn] : toReconnect)
    {
        LOGD << "Reconnecting connection " << index;
        conn->Connect(m_host.c_str(), m_port, m_user.c_str(), 
                     m_password.c_str(), m_database.c_str());
    }
}

bool NSMySQLConnectionPool::IsHealthy() const
{
    if (!m_initialized.load() || m_shutting_down.load())
        return false;

    // Quick check without extensive locking
    std::lock_guard<std::mutex> lock(m_connectionsMutex);
    
    // Just check if we have connections, don't call IsConnected under lock
    return !m_connections.empty();
}

std::shared_ptr<NSMySQLConnection> NSMySQLConnectionPool::CreateConnection()
{
    auto conn = std::make_shared<NSMySQLConnection>();
    
    if (!conn->Connect(m_host.c_str(), m_port, m_user.c_str(), 
                      m_password.c_str(), m_database.c_str()))
    {
        LOGE << "Failed to connect to " << m_host << ":" << m_port << "/" << m_database;
        m_failedConnections++;
        return nullptr;
    }

    // Set connection options
    conn->SetAutoCommit(true);
    conn->SetCharacterSet("utf8mb4");
    
    m_totalQueries++;
    
    return conn;
}

bool NSMySQLConnectionPool::LoadConnectionInfo()
{
    // This would load from config file or environment
    // For now, return false to indicate it needs to be set manually
    return false;
}

bool NSMySQLConnectionPool::ValidateConnection(const std::shared_ptr<NSMySQLConnection>& conn)
{
    if (!conn)
        return false;

    // Just check connection pointer validity
    // Don't do network operations (Ping) here
    return conn->IsConnected();
}