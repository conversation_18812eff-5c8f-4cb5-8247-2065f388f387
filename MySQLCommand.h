#pragma once
#include <string>
#include <unordered_map>
#include <variant>
#include <vector>
#include <memory>
#include <mysql.h>
#include "MySQLCompatibility.h"
#include "mimalloc_integration.h"

// 심플한 MySQL Command (레거시 호환 인터페이스만 제공)
class MySQLCommand
{
public:
    using ValueType = std::variant<int32_t, int64_t, float, double, std::string, std::vector<uint8_t>>;

    MySQLCommand();
    ~MySQLCommand();

    // 레거시 호환 인터페이스 - MakeQuery에서 사용
    void SetItem(const std::string& name, int32_t value) { m_parameters[name] = value; }
    void SetItem(const std::string& name, int64_t value) { m_parameters[name] = value; }
    void SetItem(const std::string& name, float value) { m_parameters[name] = value; }
    void SetItem(const std::string& name, double value) { m_parameters[name] = value; }
    void SetItem(const std::string& name, const std::string& value) { m_parameters[name] = value; }
    void SetItem(const std::string& name, const char* value) { m_parameters[name] = std::string(value); }
    void SetItem(const std::string& name, const std::vector<uint8_t>& value) { m_parameters[name] = value; }
    
    // Move semantics 오버로드 - 복사 방지
    void SetItem(const std::string& name, std::string&& value) { m_parameters[name] = std::move(value); }
    void SetItem(const std::string& name, std::vector<uint8_t>&& value) { m_parameters[name] = std::move(value); }

    // 레거시 호환 인터페이스 - MakeOutput에서 사용
    bool GetItem(const std::string& name, int32_t& value);
    bool GetItem(const std::string& name, int64_t& value);
    bool GetItem(const std::string& name, float& value);
    bool GetItem(const std::string& name, double& value);
    bool GetItem(const std::string& name, std::string& value);
    bool GetItem(const std::string& name, std::vector<uint8_t>& value);

    // 내부용 - MySQL 바인딩
    void BindToMySQL(MYSQL_STMT* stmt, std::vector<MYSQL_BIND>& binds);  // MySQL C API 호환성
    void RetrieveOutputParameters(MYSQL_STMT* stmt);

    // 파라미터 정보
    const std::unordered_map<std::string, ValueType>& GetParameters() const { return m_parameters; }
    void ClearParameters() { m_parameters.clear(); m_outputParameters.clear(); }
    
    // 메모리 풀용 리셋
    void Reset() { ClearParameters(); }

private:
    mi::unordered_map<std::string, ValueType> m_parameters;      // mimalloc 사용
    mi::unordered_map<std::string, ValueType> m_outputParameters; // mimalloc 사용
    
    // MySQL 바인딩을 위한 데이터 저장소
    struct BindData
    {
        MYSQL_BIND bind{};
        ValueType data;
        std::vector<uint8_t> buffer;  // string/blob용 버퍼 (MySQL API 호환)
        unsigned long length = 0;
        mysql_bool_compat is_null = 0;
        
        void PrepareBinding();
    };
    
    mi::vector<BindData> m_bindData;  // mimalloc 사용
};