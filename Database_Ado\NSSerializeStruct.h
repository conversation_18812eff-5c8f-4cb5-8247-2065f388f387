#pragma once

#include "NPDefineConst.h"
#include "NPDefineEnum.h"
#include "NSDefineEnum.h"
#include "NPDataTable.h"
#include "Type/NpLib_InstanceKey.h"
#include "Type/NpLib_UnitServerId.h"
#pragma pack(push, 1)

template<typename _Type>
struct SerializeTraits;

struct NSSerializeHeader
{
	//DataSize = 헤더가 포함된 사이즈
	size_t ComponentCount = 0;
	size_t DataSize = 0;
};

struct NSSerializeComponentHeader
{
	//DataSize = 헤더가 포함된 사이즈
	int32_t ComponentId = 0;
	size_t DataSize = 0;
};

struct NSSerializeComponentDataHeader
{
	size_t DataCount = 0;
	size_t DataTypeId = 0;
	size_t SizePerData = 0;
};

/* AchievementComponent */
struct NSAchievementGroupClearStatusSerializeData
{
	int32_t AchievementGroupDataID;
	bool HasCleared;
};
template<>
struct SerializeTraits<NSAchievementGroupClearStatusSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSAchievementGroupClearStatusSerializeData;
};

struct NSAchievementSerializeData
{
	NPAchievement Info;
};
template<>
struct SerializeTraits<NSAchievementSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSAchievementSerializeData;
};

/* HeroItemComponent */
struct NSItemSerializeData
{
	int64_t ItemUid = 0;
	int32_t ItemId = 0;
	uint8_t Location = 0;
	int32_t Quantity = 0;
	bool IsMarketPreserved = false;
	uint64_t UpdateAt = 0;
	uint64_t CreateAt = 0;
	uint8_t BelongType = 0;
	int32_t Quality = 0;
};
template<>
struct SerializeTraits<NSItemSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSItemSerializeData;
};

struct NSItemOptionSerializeData
{
	int64_t ItemUid = 0;
	uint16_t OptionKey = 0;
	uint16_t OptionIdx = 0;
	int64_t OptionValue = 0;
};
template<>
struct SerializeTraits<NSItemOptionSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSItemOptionSerializeData;
};

struct NSItemCooldownSerializeData
{
	int32_t CooldownGroup = 0;
	uint64_t NextCooldown = 0;
};
template<>
struct SerializeTraits<NSItemCooldownSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSItemCooldownSerializeData;
};

struct NSInventorySlotSerializeData
{
	uint8_t Slot = 0;
	int64_t ItemUid = 0;
	int32_t ItemId = 0;
};
template<>
struct SerializeTraits<NSInventorySlotSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSInventorySlotSerializeData;
};

/* HeroGoodsComponent */
struct NSHeroGoodsSerializeData
{
	ENpLib_MoneyType MoneyType;
	MONEY_TYPE Quantity;
};
template<>
struct SerializeTraits<NSHeroGoodsSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSHeroGoodsSerializeData;
};

/* HeroPointComponent */
struct NSHeroPointsSerializeData
{
	ENpLib_CharacterPointType PointType;
	int32_t Value;
};
template<>
struct SerializeTraits<NSHeroPointsSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSHeroPointsSerializeData;
};

/* HeroDataComponent */
struct NSHeroDataSerializeData
{
	NSCharacterData CharacterData;
};
template<>
struct SerializeTraits<NSHeroDataSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSHeroDataSerializeData;
};

/* StableComponent */
struct NSStableSerializeData
{
	int32_t	VehicleDataID;
	bool	CurrentSelected;
	int64_t ContractedAt;
	int64_t WillbeExpiredAt;
};
template<>
struct SerializeTraits<NSStableSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSStableSerializeData;
};

/* ItemCraftComponent */
struct NSItemCraftSerializeData
{
	int32_t TargetCraftID;
	int64_t TimeUnlocked;
	bool	Activated;
};
template<>
struct SerializeTraits<NSItemCraftSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSItemCraftSerializeData;
};

/* AbilityComponent */
struct NSAbilitySerializeData
{
	ENpLib_AbilityType AbilityType;
	int32_t Level;
	int32_t Exp;
	bool Specialized;
	int64_t AccumulatedExp;
};
template<>
struct SerializeTraits<NSAbilitySerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSAbilitySerializeData;
};

/* ReturnPointComponent */
struct NSReturnPointSerializeData
{
	int32_t	ReturnPointId;
	bool	Activated;
};
template<>
struct SerializeTraits<NSReturnPointSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSReturnPointSerializeData;
};

/* ChronotectorActionComponent */
struct NSChronotectorActionSerializeData
{
	int32_t		ActionId;
	bool		IsOnSlot;
	uint64_t	CooldownEndTime;
};
template<>
struct SerializeTraits<NSChronotectorActionSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSChronotectorActionSerializeData;
};

/* AppearanceComponent */
struct NSAppearanceSerializeData
{
	uint32_t	Key;
	uint8_t		Value;
};
template<>
struct SerializeTraits<NSAppearanceSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSAppearanceSerializeData;
};

/* ContentUnlockComponent */
struct NSContentUnlockSerializeDataLegacy
{
	ENpLib_ContentUnlockType	UnlockContentType;
	int32_t						ContentValue;
	uint64_t					RegistUnixTime;
};
template<>
struct SerializeTraits<NSContentUnlockSerializeDataLegacy>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSContentUnlockSerializeDataLegacy;
};

struct NSContentUnlockSerializeData
{
	int32_t	DataId;
};
template<>
struct SerializeTraits<NSContentUnlockSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSContentUnlockSerializeData;
};

/* DungeonComponent */
struct NSDungeonSerializeData
{
	int32_t		DungeonId;
	ENpLib_DungeonTryState TryState;
	int32_t		ChallengeClear[g_iChallengeInfoMaxCount];
};
template<>
struct SerializeTraits<NSDungeonSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSDungeonSerializeData;
};

/* DungeonComponent */
struct NSDungeonMatchSerializeData
{
	int32_t MatchVoteCancelCount;
	uint64_t MatchVoteCancelResetTime;
	uint64_t MatchPenaltyTime;
};
template<>
struct SerializeTraits<NSDungeonMatchSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSDungeonMatchSerializeData;
};

/* DungeonRecordComponent */
struct NSCharacterDungeonRecordSerializeData
{
	int32_t TotalDungeonEnters;		//! 입장 횟수
	int32_t TotalEscapesSucceeded;	//! 탈출에 성공한 횟수
	int32_t TotalPlayerKills;		//! 누적 플레이어 처치 수
	int64_t TotalMonsterKills;		//! 누적 몬스터 처치 수
	int64_t TotalInteractions;		//! 누적 인터랙션 횟수
	int64_t TotalDistanceMoved;		//! 누적 이동거리
	int64_t AverageEscapeTime;		//! 평균 탈출 성공 시간
	int32_t LongestEscapeTime;		//! 가장 늦은 탈출 시간
};

template<>
struct SerializeTraits<NSCharacterDungeonRecordSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCharacterDungeonRecordSerializeData;
};

/* CoolTimeComponent */
struct NSCoolTimeSerializeData
{
	ECoolTimeContentsType	CoolTimeContentsType;
	int64_t					SubId;
	uint64_t				ExpireTime;
};
template<>
struct SerializeTraits<NSCoolTimeSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCoolTimeSerializeData;
};

/* CharacterDataComponent */
struct NSCharacterDataSerializeData
{
	int32_t CharacterDataId;
};
template<>
struct SerializeTraits<NSCharacterDataSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCharacterDataSerializeData;
};

/* InitContentsComponent */
struct NSInitContentsSerializeData
{
	ENpLib_InitContentsType	ContentsType;
	int32_t					InitCount;
};
template<>
struct SerializeTraits<NSInitContentsSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSInitContentsSerializeData;
};

/* QuestComponent */
struct NSActivateQuestSerializeData
{
	int32_t	QuestId;
	bool	IsActivate;
};
template<>
struct SerializeTraits<NSActivateQuestSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSActivateQuestSerializeData;
};

struct NSInProgressQuestSerializeData
{
	int32_t				QuestId;
	uint64_t			QuestFailTime;
	ENpLib_QuestState	State;
	int32_t				StepId;
	uint64_t			StepFailTime;
	int32_t				TargetCounts[g_uMaxQuestStepTargetCount] = { 0, };
};
template<>
struct SerializeTraits<NSInProgressQuestSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSInProgressQuestSerializeData;
};

struct NSCompletedQuestSerializeData
{
	int32_t		QuestId = 0;
	int32_t		CompletedCount = 0;
	int32_t		TotalCompletedCount = 0;
	uint64_t	NextResetTime = 0;
};
template<>
struct SerializeTraits<NSCompletedQuestSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCompletedQuestSerializeData;
};

struct NSCompletedQuestStepSerializeData
{
	int32_t		QuestId{ 0 };
	char		QuestStepCodes[g_uMaxCompletedQuestStepCount]{};
};
template<>
struct SerializeTraits<NSCompletedQuestStepSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCompletedQuestStepSerializeData;
};

struct NSDailyBonusRequestSerializeData
{
	int32_t FactionQuestDailyBonusRemainCount;
	int64_t DailyBonusNextResetTime;
};
template<>
struct SerializeTraits<NSDailyBonusRequestSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSDailyBonusRequestSerializeData;
};

/* ChronotectorNodeComponent */
struct NSChronotectorNodeSerializeData
{
	int32_t NodeTid;
	int32_t JewelItemId;
	bool IsActivate;
};
template<>
struct SerializeTraits<NSChronotectorNodeSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSChronotectorNodeSerializeData;
};

/* HeroUnitDataComponent */
struct NSHeroUnitDataSerializeData
{
	int32_t CharacterDataId{ 0 };
	int64_t AId{ 0 };
	int64_t CId{ 0 };
	int32_t CharacterTitleId{ 0 };
	char Name[g_uMaxCharacterNameUTF8Length]{};
	uint8_t Gender{ 0 };
	uint8_t FacialValue{ 0 };

	ENPPlatformType PlatformType{ 0 };
	char PlatformID[g_uMaxPlatformIDLength]{};
	ENPPlatformType IdentityProviderType{ 0 };
	char IdentityProviderID[g_uMaxPlatformIDLength]{};
};
template<>
struct SerializeTraits<NSHeroUnitDataSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSHeroUnitDataSerializeData;
};

/* TerritoryStandingComponent */
struct NSTerritoryStandingSerializeData
{
	int32_t TerritoryId = 0;
	int32_t StandingExp = 0;
	int32_t StandingLevel = 0;
	int32_t StandingPoint = 0;
};
template<>
struct SerializeTraits<NSTerritoryStandingSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSTerritoryStandingSerializeData;
};

/* HouseComponent */
struct NSHouseSerializeData
{
	int32_t		HouseId;
	uint64_t	ContractDate;
	bool		Activated;
};
template<>
struct SerializeTraits<NSHouseSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSHouseSerializeData;
};

/* DocumentPropComponent */
struct NSDocumentPropSerializeData
{
	NPDocumentProp PublicData;
	bool Activated{ false };
};
template<>
struct SerializeTraits<NSDocumentPropSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSDocumentPropSerializeData;
};

/* MarketComponent */
struct NSMarketSerializeData
{
	NPMarketDeal Deal;
};
template<>
struct SerializeTraits<NSMarketSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSMarketSerializeData;
};

/* OwnedCampComponent */
struct NSOwnedCampSerializeData
{
	int32_t TableDataId;
};
template<>
struct SerializeTraits<NSOwnedCampSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSOwnedCampSerializeData;
};

/* EmotionComponent */
struct NSEmotionSerializeData
{
	int32_t EmotionDataId;
	EEmotionStrType EmotionStrType;
	char StrData[g_uMaxEmotionCommandUTF8Length] = { 0, };
	bool IsDefault;
};
template<>
struct SerializeTraits<NSEmotionSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSEmotionSerializeData;
};

struct NSEmotionSlotSerializeData
{
	int32_t		EmotionDataId;
	uint8_t		Index;
};
template<>
struct SerializeTraits<NSEmotionSlotSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSEmotionSlotSerializeData;
};

/* GuildComponent */
struct NSGuildSerializeData
{
	int64_t MyCid;
};
template<>
struct SerializeTraits<NSGuildSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSGuildSerializeData;
};

/* ClassPointComponent */
struct NSClassPointSerializeData
{
	int32_t CharacterDataId;
	int32_t Level;
	int32_t CP1;
	int32_t CP2;
	int32_t CP3;
};
template<>
struct SerializeTraits<NSClassPointSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSClassPointSerializeData;
};

/* WeaponMasteryComponent */
struct NSWeaponMasteryBoardSerializeData
{
	ENpLib_CharacterWeaponType WeaponType;
	int32_t Level;
	int32_t Point;
	int32_t Exp;
	int32_t NextExp;
};
template<>
struct SerializeTraits<NSWeaponMasteryBoardSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSWeaponMasteryBoardSerializeData;
};

struct NSWeaponMasterySkillSerializeData
{
	int32_t MasteryId;
	int32_t Level;
};
template<>
struct SerializeTraits<NSWeaponMasterySkillSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSWeaponMasterySkillSerializeData;
};

/* StoreUsageComponent */
struct NSStoreUsageSerializeData
{
	int64_t AID;
	int64_t CID;
	int32_t DataID;
	int32_t Quantity;
	uint64_t ResetTime;
};
template<>
struct SerializeTraits<NSStoreUsageSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSStoreUsageSerializeData;
};

/* ChrnotectorUpgradeComponent */
struct NSChronotectorUpgradeSerializeData
{
	int32_t UpgradeId;
	bool	Activated;
};
template<>
struct SerializeTraits<NSChronotectorUpgradeSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSChronotectorUpgradeSerializeData;
};

struct NSChronotectorUpgradePointSerializeData
{
	int32_t Point;
};
template<>
struct SerializeTraits<NSChronotectorUpgradePointSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSChronotectorUpgradePointSerializeData;
};

/* PlayTimeComponent */
struct NSPlayTimeSerializeData
{
	int32_t PrevPlayTimeMin;
	uint64_t StartPlayTimeTickMS;
};
template<>
struct SerializeTraits<NSPlayTimeSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSPlayTimeSerializeData;
};

struct NSRelationSerializeData
{
	ENpLib_CharacterRelationType CharacterRelation;
	ENpLib_CharacterRelationType OverrideRelation;
	bool	PvP;
};
template<>
struct SerializeTraits<NSRelationSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSRelationSerializeData;
};

struct NSGmCommandSerializeData
{
	bool ShowCollision;
	bool ShowMotionPosition;
	bool Invincible;
	bool ShowAggroList;
	bool ShowDamageLog;
	bool NoCooldown;
	bool IsRegen;
	bool NoRegen;
	bool IsNoCostCP;
	bool IsNoCostStamina;
	bool ShowWeaponDurability;
	bool BtNodeInfo;
	bool ShowDebugPassive;
	bool ShowTriggerVolume;
	bool DisableConsumeItemDurability;
	int AttackDamage;
	int StaggerPoint;
	int WeakKnockBackCost;
	uint8 AttackOnHitState;
	uint8 HitOnHitState;
	bool NoAggro;
	int32_t RandomDamageRange;
};
template<>
struct SerializeTraits<NSGmCommandSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSGmCommandSerializeData;
};

struct NSSaveJsonFormatDataSerializeData
{
	int8_t SaveJsonFormatDataId = 0;
	char SaveJsonFormatDataString[g_uStringSaveJsonFormatDataMaxSize] = { 0, };
};
template<>
struct SerializeTraits<NSSaveJsonFormatDataSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSSaveJsonFormatDataSerializeData;
};

struct NSPassiveCoolDownSerializeData
{
	int32_t PassiveGroupId;
	uint64_t CooldownTick;
};
template<>
struct SerializeTraits<NSPassiveCoolDownSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSPassiveCoolDownSerializeData;
};

/* ImmuneComponent */
struct NSImmuneSerializeData
{
	ENpLib_ImmuneType ImmuneType;
	int32_t Count;
};
template<>
struct SerializeTraits<NSImmuneSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSImmuneSerializeData;
};

/* ActiveSlotComponent */
struct NSActiveSlotWeaponMasterySerializeData
{
	NPActiveSlotWeaponMastery ActiveSlotWeaponMastery;
};
template<>
struct SerializeTraits<NSActiveSlotWeaponMasterySerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSActiveSlotWeaponMasterySerializeData;
};

/* CharacterTerritoryComponent */
struct NSCharacterStructureUpgradeSerializeData
{
	ENpLib_TerritoryUpgradeDetailType StructureUpgradeType = ENpLib_TerritoryUpgradeDetailType::None;
	int32_t Tier = 0;
};
template<>
struct SerializeTraits<NSCharacterStructureUpgradeSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCharacterStructureUpgradeSerializeData;
};

struct NSCharacterLifeStyleUpgradeSerializeData
{
	ENpLib_TerritoryUpgradeDetailType LifeStyleUpgradeType = ENpLib_TerritoryUpgradeDetailType::None;
	uint64_t ExpireTimestamp = 0;
};
template<>
struct SerializeTraits<NSCharacterLifeStyleUpgradeSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCharacterLifeStyleUpgradeSerializeData;
};

struct NSStatusEffectSerializeData
{
	//NSStatusEffectData -> Template으로 나머지 찾을 수 있음.
	int32_t DataID{ 0 };

	uint64_t Creation{ 0 };
	uint64_t Duration{ 0 };

	//내부 데이터
	int64_t CasterUnitServerId{ 0 };
	int64_t EffectiveCasterUnitServerId{ 0 };
	int32_t CasterAttack{ 0 };
	int32_t SkillDamage{ 0 };
	int32_t SkillDataId{ 0 };
	int32_t ExecId{ 0 };
	int32_t StackCount{ 0 };
	int32_t PassiveDataId{ 0 };
	int64_t FromItemUid{ 0 };
	int64_t CasterId{ 0 };
	int32_t ExecDamages{ 0 };

	int32_t OverTimeCountLength{ 0 };
	int32_t StatSnapshotType{ 0 };
};
template<>
struct SerializeTraits<NSStatusEffectSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSStatusEffectSerializeData;
};

struct NSStatusEffectSerializeDataAttackSnapshot
{
	int64_t CasterId = 0;
	double Attack = 0;
	EEntityType EntityType = EEntityType::None;
	ENpLib_CharacterClassType ClassType = ENpLib_CharacterClassType::None;
	int32_t Level = 1;
	int32_t RandomRange = 0; //optional

	ENpLib_UnitSubType UnitSubType = ENpLib_UnitSubType::None;
	double CriticalHitDamage = 0;
	double IncreaseDamage = 0;
	double IncreaseDamageNormalMob = 0;
	double IncreaseDamageBoss = 0;
	double IncreaseDamageHero = 0;
	double IncreaseSkillDamage = 0;
	double IncreaseNormalDamage = 0;
	double DOTAttackPower = 0;
	double DamageDebuffFlameEnhance = 0;
	double DamageDebuffColdEnhance = 0;
	double DamageDebuffElectricEnhance = 0;
	double DamageDebuffPoisonEnhance = 0;
	double DamageDebuffBleedEnhance = 0;
	double StaggerEnforce = 0;
};
template<>
struct SerializeTraits<NSStatusEffectSerializeDataAttackSnapshot>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSStatusEffectSerializeDataAttackSnapshot;
};

struct NSStatusEffectSerializeDataHealSnapshot
{
	int64_t CasterEntityId = 0;
	double CasterHeal = 0.0;
	double CasterPotionHealIncrease = 0.0;
	double CasterSkillLevelUpValueHot1Fixed = 0.0;
	double CasterSkillLevelUpValueHot1Rate = 0.0;
	double CasterReceiveNormalHealFixedModifier = 0.0;
	double CasterReceiveNormalHealRatioModifier = 0.0;
	double CasterReceivePotionHealFixedModifier = 0.0;
	double CasterReceivePotionHealRatioModifier = 0.0;
	double CasterReceiveNormalHealByLostLifeRatioModifier = 0.0;
	double CasterReceiveNormalHealRatioAfterCalculateIncreaseReceiveHealModifier = 0.0;
	double CasterReceivePotionHealByLostLifeRatioModifier = 0.0;
	double CasterReceivePotionHealRatioAfterCalculateIncreaseReceiveHealModifier = 0.0;
	double CasterHPRate = 0.0;

	int64_t TargetEntityId = 0;
	double TargetHeal = 0.0;
	double TargetPotionHealIncrease = 0.0;
	double TargetSkillLevelUpValueHot1Fixed = 0.0;
	double TargetSkillLevelUpValueHot1Rate = 0.0;
	double TargetReceiveNormalHealFixedModifier = 0.0;
	double TargetReceiveNormalHealRatioModifier = 0.0;
	double TargetReceivePotionHealFixedModifier = 0.0;
	double TargetReceivePotionHealRatioModifier = 0.0;
	double TargetReceiveNormalHealByLostLifeRatioModifier = 0.0;
	double TargetReceiveNormalHealRatioAfterCalculateIncreaseReceiveHealModifier = 0.0;
	double TargetReceivePotionHealByLostLifeRatioModifier = 0.0;
	double TargetReceivePotionHealRatioAfterCalculateIncreaseReceiveHealModifier = 0.0;
	double TargetHPRate = 0.0;
};
template<>
struct SerializeTraits<NSStatusEffectSerializeDataHealSnapshot>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSStatusEffectSerializeDataHealSnapshot;
};

struct NSStatusEffectSerializeDataOverTimeCount
{
	int32_t key;
	int32_t value;
};
template<>
struct SerializeTraits<NSStatusEffectSerializeDataOverTimeCount>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSStatusEffectSerializeDataOverTimeCount;
};

//SkillSetComponent
struct NSWeaponMasteryPassiveSkillSetSerializeData
{
	ENpLib_CharacterWeaponType WeaponType;
	int32_t PassiveSkill;
};
template<>
struct SerializeTraits<NSWeaponMasteryPassiveSkillSetSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSWeaponMasteryPassiveSkillSetSerializeData;
};

struct NSWeaponMasteryActiveSkillSetSerializeData
{
	ENpLib_CharacterWeaponType WeaponType;
	int32_t MasteryId;
	int32_t Level;
};
template<>
struct SerializeTraits<NSWeaponMasteryActiveSkillSetSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSWeaponMasteryActiveSkillSetSerializeData;
};

struct NSWeaponMasteryBaseSkillSetSerializeData
{
	ENpLib_CharacterWeaponType WeaponType;
	int32_t BaseSkill;
};
template<>
struct SerializeTraits<NSWeaponMasteryBaseSkillSetSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSWeaponMasteryBaseSkillSetSerializeData;
};

struct NSModChangeSkillSetSerializeData
{
	int32_t ModChangeSkill;
};
template<>
struct SerializeTraits<NSModChangeSkillSetSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSModChangeSkillSetSerializeData;
};

struct NSMonsterSoulSerializeData
{
	int32_t	MonsterSoulId;
	int32_t	EnchantLevel;
	bool CurrentSelected;
	bool CurrentSummoned;
	bool Activated;
};
template<>
struct SerializeTraits<NSMonsterSoulSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSMonsterSoulSerializeData;
};

struct NSSettingsSerializeData
{
	bool ShowsProfile;
};
template<>
struct SerializeTraits<NSSettingsSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSSettingsSerializeData;
};

struct NSCostumeSerializeData
{
	int32_t CostumeID;
	bool IsEquipped;
};

template<>
struct SerializeTraits<NSCostumeSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCostumeSerializeData;
};

struct NSCostumePresetSerializeData
{
	uint64_t PresetID{ 0 };
	NPCostumeSet CostumeSet;
};

template<>
struct SerializeTraits<NSCostumePresetSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCostumePresetSerializeData;
};

struct NSCharacterMatrixContentsRecordSerializeData
{
	int32_t TotalMatrixExpAccquired;
};

template<>
struct SerializeTraits<NSCharacterMatrixContentsRecordSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCharacterMatrixContentsRecordSerializeData;
};

struct NSCharacterMatrixContentsCoresRecordSerializeData
{
	int32_t DataID;
	bool Activated;
};

template<>
struct SerializeTraits<NSCharacterMatrixContentsCoresRecordSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCharacterMatrixContentsCoresRecordSerializeData;
};

struct NSUniquePerkSerializeData
{
	int32_t UniquePerkID;
};

template<>
struct SerializeTraits<NSUniquePerkSerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSCharacterUniqeuPerkSerializeData;
};

struct NSRubySerializeData
{
	uint64_t FreeRubyAmount = 0;
	uint64_t PaidRubyAmount = 0;
};

template<>
struct SerializeTraits<NSRubySerializeData>
{
	static constexpr ESerializeId SerializeId = ESerializeId::NSRubySerializeData;
};

#pragma pack(pop)