#pragma once

#include "QueryData/NSDataSerializerBufferPool.h"
#include "NSLogDefine.h"
#include <queue>
#include <format>

#define DEF_SERIALIZER_BUFFER_SIZE 256

class NSDataSerializer
{
private:
	std::queue<NSDataSerializerBufferBase>  m_queueBuf;
	std::queue<size_t>						m_queueBufSize;
	size_t									m_CurrentPos;
	int										m_iBufferSize;

public:
	NSDataSerializer(const NSDataSerializer&) = delete;
	NSDataSerializer& operator=(const NSDataSerializer&) = delete;

	NSDataSerializer(NSDataSerializer&&) noexcept = default;
	NSDataSerializer& operator=(NSDataSerializer&&) noexcept = default;

	NSDataSerializer()
		: m_CurrentPos(0)
	{
		m_iBufferSize = DEF_SERIALIZER_BUFFER_SIZE;
	}
	~NSDataSerializer()
	{
	}

	bool IsEmpty() { return (m_queueBuf.empty()); }

	void Clear()
	{
		while (!m_queueBuf.empty())
		{
			NSDataSerializerBufferBase pcBuffer = std::move(m_queueBuf.front());
			m_queueBuf.pop();
			m_queueBufSize.pop();
		}

		m_CurrentPos = 0;
	}

	template <typename _T>
	NSDataSerializer& operator<<(const _T& cArg)
	{
		size_t iSize = (size_t)sizeof(_T);

		NSDataSerializerBufferBase buffer(static_cast<int32_t>(iSize));

		if (buffer.GetMaxSize() < iSize)
		{
			LOGE << std::format("BufferMax Size Over - Size : {}", iSize);
			return *this;
		}

		char* pcurPos = buffer.GetBuffer();
		memcpy(pcurPos, &cArg, sizeof(_T));
		m_queueBuf.push(std::move(buffer));
		m_queueBufSize.push(sizeof(_T));
		m_CurrentPos += iSize;

		return *this;
	}

	NSDataSerializer& operator<<(const std::string& strArg)
	{
		return this->operator<<(strArg.c_str());
	}

	NSDataSerializer& operator<<(const char* strArg)
	{
		size_t						iLength = strlen(strArg) + 1;
		NSDataSerializerBufferBase buffer((int)iLength);

		if (buffer.GetMaxSize() < iLength)
		{
			LOGE << std::format("BufferMax Size Over - Size : {}", iLength);
			return *this;
		}
		buffer.SetSize(static_cast<int32_t>(iLength));

		char* pcurPos = buffer.GetBuffer();
		size_t iDataSize = sizeof(char) * (iLength);
		memcpy(pcurPos, strArg, iDataSize);
		m_queueBuf.push(std::move(buffer));
		m_queueBufSize.push(iDataSize);
		m_CurrentPos += iDataSize;
		return *this;
	}

	template <typename _T>
	NSDataSerializer& operator>>(_T& cResult)
	{
		if (!m_queueBuf.empty())
		{
			NSDataSerializerBufferBase buffer = std::move(m_queueBuf.front());
			size_t						iSize = m_queueBufSize.front();

			if (sizeof(_T) < iSize)
			{
				LOGE << std::format("Size Miss Match - S: {}, T:{}", iSize, buffer.GetSize());
				return *this;
			}
			memcpy(&cResult, buffer.GetBuffer(), iSize);
			m_CurrentPos -= iSize;
			m_queueBuf.pop();
			m_queueBufSize.pop();
		}
		return *this;
	}

	NSDataSerializer& operator>>(wchar_t* wstrResult)
	{
		if (!m_queueBuf.empty() && wstrResult)
		{
			NSDataSerializerBufferBase buffer = std::move(m_queueBuf.front());
			size_t						iSize = m_queueBufSize.front();

			if (buffer.GetSize() < iSize)
			{
				LOGE << std::format("Size Miss Match - S: {}, T:{}", iSize, buffer.GetSize());
				return *this;
			}

			memcpy(wstrResult, buffer.GetBuffer(), iSize);
			m_CurrentPos -= iSize;
			m_queueBuf.pop();
			m_queueBufSize.pop();
		}
		return *this;
	}

	NSDataSerializer& operator>>(char* strResult)
	{
		if (!m_queueBuf.empty() && strResult)
		{
			NSDataSerializerBufferBase pcBuffer = std::move(m_queueBuf.front());
			size_t						iSize = m_queueBufSize.front();
			if (pcBuffer.GetSize() < iSize)
			{
				LOGE << std::format("Size Miss Match - S: {}, T:{}", iSize, pcBuffer.GetSize());
				return *this;
			}
			memcpy(strResult, pcBuffer.GetBuffer(), iSize);
			m_CurrentPos -= iSize;
			m_queueBuf.pop();
			m_queueBufSize.pop();
		}
		return *this;
	}

	NSDataSerializer& operator>>(std::string& strResult)
	{
		if (!m_queueBuf.empty())
		{
			NSDataSerializerBufferBase buffer = std::move(m_queueBuf.front());
			size_t						iSize = m_queueBufSize.front();
			//if (pcBuffer->GetSize() < iSize)
			//{
			//	LOGE << std::format("Size Miss Match - S: {}, T:{}", iSize, pcBuffer->GetSize());
			//	return *this;
			//}
			//memcpy(strResult, pcBuffer->GetBuffer(), iSize);
			strResult.assign(buffer.GetBuffer(), iSize - 1);
			m_CurrentPos -= iSize;
			m_queueBuf.pop();
			m_queueBufSize.pop();
		}
		return *this;
	}

	template <typename _Type>
	NSDataSerializer& SetPacket(_Type& packet)
	{
		int iSize = (int)packet.CalcSize();

		NSDataSerializerBufferBase buffer(iSize);

		if (buffer.GetMaxSize() < iSize)
		{
			LOGE << std::format("BufferMax Size Over - Size : {}", iSize);
			return *this;
		}

		char* pcurPos = buffer.GetBuffer();
		packet.Serialize(pcurPos);

		m_queueBuf.push(std::move(buffer));
		m_queueBufSize.push(iSize);
		m_CurrentPos += iSize;

		return *this;
	}

	template <typename _Type>
	NSDataSerializer& GetPacket(_Type& packet)
	{
		if (m_queueBuf.empty())
			return *this;

		NSDataSerializerBufferBase pcBuffer = std::move(m_queueBuf.front());
		size_t						iSize = m_queueBufSize.front();

		m_CurrentPos -= iSize;
		m_queueBuf.pop();
		m_queueBufSize.pop();

		packet.Deserialize(pcBuffer.GetBuffer());

		return *this;
	}

	template <typename _Type>
	void CopyFrontPacket(_Type& packet)
	{
		if (m_queueBuf.empty())
			return;

		NSDataSerializerBufferBase pcBuffer = std::move(m_queueBuf.front());
		packet.Deserialize(pcBuffer.GetBuffer());
	}

	template<typename T>
	T* Alloc()
	{
		auto dataSize = sizeof(T);
		NSDataSerializerBufferBase buffer(static_cast<int>(dataSize));

		if (buffer.GetMaxSize() < dataSize)
		{
			LOGE << std::format("BufferMax Size Over - Size : {}", dataSize);
			return nullptr;
		}

		char* ptr = buffer.GetBuffer();

		m_queueBuf.push(std::move(buffer));
		m_queueBufSize.push(dataSize);
		m_CurrentPos += dataSize;

		return reinterpret_cast<T*>(ptr);
	}

	template<typename T, typename... Args>
	T* Create(Args... args)
	{
		auto alloc = Alloc<T>();
		if (nullptr == alloc)
		{
			return nullptr;
		}

		return ::new (alloc) T(args ...);
	}

	template<typename T>
	T* Front()
	{
		if (true == m_queueBuf.empty())
		{
			return nullptr;
		}

		NSDataSerializerBufferBase& buffer = m_queueBuf.front();
		auto dataSize = m_queueBufSize.front();

		if (sizeof(T) != dataSize)
		{
			LOGE << std::format("Size Miss Match - S: {}, T:{}", dataSize, sizeof(T));
			return nullptr;
		}

		return reinterpret_cast<T*>(buffer.GetBuffer());
	}

	bool Next()
	{
		if (true == m_queueBuf.empty())
		{
			return false;
		}

		NSDataSerializerBufferBase buffer = std::move(m_queueBuf.front());
		auto dataSize = m_queueBufSize.front();

		m_CurrentPos -= dataSize;
		m_queueBuf.pop();
		m_queueBufSize.pop();
		return true;
	}
};
