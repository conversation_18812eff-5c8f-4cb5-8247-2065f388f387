#pragma once

#include "NSAdoDefine.h"
#include "NSDefine.h"

struct NPDateTime;
class NSPQueryData;
class NSAdoRecordset;
class NSAdoCommand
{
public:
	enum enExecute
	{
		enExecuteOk,
		enExecuteFail,
		enExecuteDisconnect,
	};

	int m_iReturnValue;

	NSAdoCommand();
	virtual ~NSAdoCommand();

	void dump_com_error(const _com_error& e, const char* pFieldName);

	virtual bool Execute(NSPQueryData* pcQueryData);
	virtual bool Execute(NSAdoRecordset* pcAdoRecordSet);
	bool Execute();

	void SetProcedure(_ConnectionPtr pConnection, char* strProcedure, int iTimeOut = 10, bool bNamedParameter = false);
	bool SetParameter(const char* pParameter, DataTypeEnum vt, ParameterDirectionEnum emDt, size_t lSize);
	void SetConnection(_ConnectionPtr pConnection);

	void Reset();

	//SET
	[[nodiscard]] virtual bool SetItem(const char* pFieldName, const char* pValue);
	virtual bool SetItem(const char* pFieldName, const bool bValue);
	virtual bool SetItem(const char* pFieldName, const uint8_t uValue);
	virtual bool SetItem(const char* pFieldName, const uint16_t uValue);
	virtual bool SetItem(const char* pFieldName, const uint32_t uValue);
	virtual bool SetItem(const char* pFieldName, const uint32_t uIndex, const uint16_t uValue);
	virtual bool SetItem(const char* pFieldName, const uint32_t uIndex, const int iValue);
	virtual bool SetItem(const char* pFieldName, const uint32_t uIndex, const uint32_t uValue);
	virtual bool SetItem(const char* pFieldName, const int iValue);
	virtual bool SetItem(const char* pFieldName, const int64_t iValue);
	virtual bool SetItem(const char* pFieldName, const uint64_t uValue);
	virtual bool SetItem(const char* pFieldName, const float fValue);
	virtual bool SetItem(const char* pFieldName, const uint32_t uIndex, const uint64_t uValue);
	virtual bool SetItem(const char* pFieldName, _variant_t& value);
	virtual bool SetItem(const char* pFieldName, SYSTEMTIME& dtTime);
	virtual bool SetItem(const char* pFieldName, NPDateTime& dtTime);
	virtual bool SetItemBinary(const char* pFieldName, byte* pValue, int iSize);

	//GET
	virtual bool GetItem(const char* pFieldName, bool& bValue);
	virtual bool GetItem(const char* pFieldName, uint16_t& iValue);
	virtual bool GetItem(const char* pFieldName, uint32_t& iValue);
	virtual bool GetItem(const char* pFieldName, uint64_t& iValue);
	virtual bool GetItem(const char* pFieldName, const uint32_t uIndex, uint8_t& iValue);
	virtual bool GetItem(const char* pFieldName, const uint32_t uIndex, uint16_t& iValue);
	virtual bool GetItem(const char* pFieldName, const uint32_t uIndex, int32_t& iValue);
	virtual bool GetItem(const char* pFieldName, const uint32_t uIndex, uint32_t& iValue);
	virtual bool GetItem(const char* pFieldName, const uint32_t uIndex, uint64_t& iValue);
	virtual bool GetItem(const char* pFieldName, uint8_t& iValue);
	virtual bool GetItem(const char* pFieldName, int& iValue);
	virtual bool GetItem(const char* pFieldName, int64_t& liValue);
	virtual bool GetItem(const char* pFieldName, float& fValue);
	virtual bool GetItem(const char* pFieldName, NPDateTime& dtTime);
	virtual bool GetItem(const char* lpszField, int8_t& iValue);
	virtual bool GetItem(const char* pFieldName, char* pValue, int iSize);
	virtual bool GetItemUTF8(const char* pFieldName, char* pValue, int iSize);
	virtual bool GetItemBinary(char* pData, byte* pValue, int iSize, const char* pFieldName);

	_CommandPtr GetCommand() { return m_pCommand; }
	const char* GetCommandName() { return m_szCommandName.c_str(); }
	void SetCommandName(const char* pCommandName) { m_szCommandName = pCommandName; }

	int GetReturnValue() const { return m_iReturnValue; }

	auto GetQueryString(std::string& strQuery) -> const char*;
	void PrintQuery();

protected:
	std::wstring GetQueryString();
	void ResultHandler(long lError = 0);
	void PrintQuery(int iReturnValue, long lError = 0);

	_CommandPtr	m_pCommand = nullptr;
	_ConnectionPtr m_pConnection = nullptr;
	std::string	m_szCommandName;

	//유니코드 버퍼
	WCHAR m_unicode[ADORECORDSET_UNICODE_BUFFER_SIZE] = { 0, };
};
