#include "stdafx.h"
#include "NSPromise.h"

#include "Promise/NSPromiseAwait.h"
#include "Detail/ThreadPool.h"

namespace Promise
{
	auto Delay(uint64_t milliseconds) -> NSPromise<void>
	{
		NSCompletionToken<void> completionToken;

		Detail::ThreadPool::GetInstance().Delay([completionToken]() mutable
			{
				completionToken.Set();
			}, milliseconds);

		return NSPromise<void>(std::move(completionToken));
	}

	auto Schedule(const std::function<void()>& function, uint64_t milliseconds) -> std::stop_source
	{
		std::stop_source source;

		NSPromise<void> promise = [/*CORE GUIDELINES CP.51 DO NOT CAPTURE*/](
			std::stop_source source,
			std::function<void()> function,
			uint64_t milliseconds) -> NSPromise<void>
		{
			std::stop_token token = source.get_token();

			while (true)
			{
				co_await Delay(milliseconds);

				if (token.stop_requested())
					break;

				function();
			}
		}(source, function, milliseconds);
		(void)promise;

		return source;
	}

	auto Schedule(NSExecutor& executor, const std::function<void()>& function, uint64_t milliseconds) -> std::stop_source
	{
		return Schedule([executor = &executor, function]()
			{
				executor->Post(function);
			}, milliseconds);
	}
}
