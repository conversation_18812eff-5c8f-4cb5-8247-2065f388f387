#include "NSStorageUpdater.h"
#include "NSStorageModel.h"
#include "NSStorageManager.h"
#include "../NSDataBaseManager.h"
#include "../NSIOCPWorkManager.h"
#include "../NSQueryData.h"
#include "../NSDBSession.h"
#include "../NSDataSerializer.h"
#include "../NSGuidManager.h"
#include "../Connection/NSMySQLConnection.h"
#include "../Command/MySQLCommand.h"
#include <thread>
#include <chrono>
#include <cassert>

// 임시 구현
namespace {
    int32_t GetWid() { return 1; } // Service::GetInstance()->GetWid() 대체
}

NSStorageUpdater::NSStorageUpdater(std::shared_ptr<NSDBSession> session, int64_t aid, int64_t cid, std::source_location location)
    : m_Wid(GetWid()),
    m_Session(session),
    m_Aid(aid),
    m_Cid(cid)
{
    LOG_DEBUG("declared from {}:{}", location.file_name(), location.line());
}

NSStorageUpdater::~NSStorageUpdater()
{
#ifdef _DEBUG
    assert(m_CommitCalled);
#endif
}

void NSStorageUpdater::Disconnect([[maybe_unused]] std::shared_ptr<NSDBSession> session)
{
#ifdef _DEBUG
    DisconnectSession(session, EErrorCode::DBError);
#endif
}

void NSStorageUpdater::MakeEscrowPayload(rapidjson::Writer<rapidjson::StringBuffer>& writer, const NSEscrowData& escrowData)
{
    writer.StartObject();
    {
        writer.Key("SID");
        writer.Int64(escrowData.SenderCid);

        writer.Key("RID");
        writer.Int64(escrowData.ReceiverCid);

        writer.Key("T");
        writer.Int(static_cast<int>(escrowData.EscrowType));

        writer.Key("I");
        writer.Int(escrowData.EscrowId);

        writer.Key("Q");
        writer.Int(escrowData.EscrowQuantity);

        writer.Key("UID");
        writer.Int64(escrowData.EscrowUid);
    }
    writer.EndObject();
}

std::vector<std::tuple<int64_t, NSStorageModel*>> NSStorageUpdater::GetModel()
{
    return m_Models;
}

std::vector<TYPE_RESULT_MODEL_FUNC> NSStorageUpdater::GetResultFunc()
{
    return m_ResultFuncs;
}

void NSStorageUpdater::AddModelToUpdate(NSStorageModel* componentModel, std::source_location location)
{
    LOG_DEBUG("called from {}:{}", location.file_name(), location.line());

    if (componentModel == nullptr)
    {
        LOG_ERROR("NSStorageUpdater::AddModelToUpdate() has called with empty component.");
        return;
    }

    int32_t typeId = componentModel->GetModelId();
    if (m_ModelIds.insert(typeId).second)
    {
        int64_t newSeq = NSStorageManager::GetInstance()->IncrementSequence(m_Cid);
        m_Models.push_back(std::make_tuple(newSeq, componentModel));

        LOG_DEBUG("Model {}({}) seq {} ", componentModel->GetModelName(), typeId, newSeq);
    }
    else
    {
        LOG_DEBUG("Model {}({}) (duplicated)", componentModel->GetModelName(), typeId);
    }
}

void NSStorageUpdater::AddResultFunc(const TYPE_RESULT_MODEL_FUNC& resultFunc)
{
    m_ResultFuncs.push_back(resultFunc);
}

void NSStorageUpdater::Rollback(std::source_location location)
{
    LOG_DEBUG("called from {}:{}", location.file_name(), location.line());

#ifdef _DEBUG
    assert(!m_CommitCalled);
    m_CommitCalled = true;
#endif

    // 롤백은 더 이상 지원하지 않음 - 단순히 모델 롤백만 수행
    for (auto& [seq, model] : m_Models)
    {
        model->Rollback();
    }
    
    LOG_INFO("NSStorageUpdater::Rollback() completed for CID {}", m_Cid);
}

void NSStorageUpdater::Commit(const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
    QueryFunc(EStorageUpdateMethod::Normal, resultFunc, location, false, 0);
}

void NSStorageUpdater::CommitForce(const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
    QueryFunc(EStorageUpdateMethod::Normal, resultFunc, location, true, 0);
}

void NSStorageUpdater::CommitWithEscrowDeposit(const int64_t escrowReceiverCid, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
    m_EscrowReceiverCid = escrowReceiverCid;
    QueryFunc(EStorageUpdateMethod::EscrowDeposit, resultFunc, location, false, 0);
}

void NSStorageUpdater::CommitWithEscrowWithdraw(int64_t escrowTransactionId, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
    QueryFunc(EStorageUpdateMethod::EscrowWithdraw, resultFunc, location, false, escrowTransactionId);
}

void NSStorageUpdater::CommitWithEscrowReclaim(const int64_t escrowTransactionId, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location)
{
    QueryFunc(EStorageUpdateMethod::EscrowReclaim, resultFunc, location, false, escrowTransactionId);
}

void NSStorageUpdater::QueryFunc(EStorageUpdateMethod method, const TYPE_RESULT_MODEL_FUNC& resultFunc, std::source_location location, bool isForce, int64_t escrowTransactionId)
{
    LOG_DEBUG("called from {}:{}", location.file_name(), location.line());

#ifdef _DEBUG
    assert(!m_CommitCalled);
    m_CommitCalled = true;
#endif

    if (method != EStorageUpdateMethod::Normal &&
        method != EStorageUpdateMethod::CustomProcedure &&
        m_Models.size() != 1)
    {
        LOG_ERROR("NSStorageUpdater::Commit() only allows 1 model with escrow. count: {}", m_Models.size());
        return;
    }

    if (resultFunc != nullptr)
    {
        AddResultFunc(resultFunc);
    }

    int64_t newEscrowTransactionId = method == EStorageUpdateMethod::EscrowDeposit
        ? NSGuidManager::GetInstance()->GenerateGuid(NSGuidManager::Guid::Escrow)
        : 0;

    size_t currentIdx = 0;
    size_t modelCount = m_Models.size();
    for (auto& [seq, model] : m_Models)
    {
        ++currentIdx;
        if (model == nullptr)
        {
            LOG_WARN("NSStorageUpdater::Commit() found a nullptr model");
            continue;
        }

        auto container = std::make_shared<NSStorageUpdateContainer>();
        container->Wid = m_Wid;
        container->Aid = m_Aid;
        container->Cid = m_Cid;
        container->Session = m_Session;
        container->Seq = 0;  // 시퀀스는 UpdateFunc에서 할당
        container->ModelId = model->GetModelId();
        container->Method = method;

        switch (method)
        {
        case EStorageUpdateMethod::Normal:
        {
            container->ProcedureName = model->GetUpsertName();
        }
        break;
        case EStorageUpdateMethod::EscrowDeposit:
        {
            NSStorageEscrowModel* escrowModel = static_cast<NSStorageEscrowModel*>(model);
            if (escrowModel == nullptr)
            {
                LOG_ERROR("Model {} is not an escrow model.", model->GetModelName());
                return;
            }
            container->ProcedureName = escrowModel->GetEscrowDepositName();
            container->EscrowTransactionId = newEscrowTransactionId;
            escrowModel->SerializeEscrow(container->EscrowPayload, m_EscrowReceiverCid);
        }
        break;
        case EStorageUpdateMethod::EscrowWithdraw:
        {
            NSStorageEscrowModel* escrowModel = static_cast<NSStorageEscrowModel*>(model);
            if (escrowModel == nullptr)
            {
                LOG_ERROR("Model {} is not an escrow model.", model->GetModelName());
                return;
            }
            container->ProcedureName = escrowModel->GetEscrowWithdrawName();
            container->EscrowTransactionId = escrowTransactionId;
        }
        break;

        case EStorageUpdateMethod::EscrowReclaim:
        {
            NSStorageEscrowModel* escrowModel = dynamic_cast<NSStorageEscrowModel*>(model);
            if (escrowModel == nullptr)
            {
                LOG_ERROR("Model {} is not an escrow model.", model->GetModelName());
                return;
            }
            container->ProcedureName = escrowModel->GetEscrowReclaimName();
            container->EscrowTransactionId = escrowTransactionId;
        }
        break;

        }

        if (container->ProcedureName.empty())
        {
            LOG_ERROR("Model {} has invalid UpsertName.", model->GetModelName());
            continue;
        }

        if (method == EStorageUpdateMethod::Normal && isForce)
        {
            model->Serialize(container->Payloads);
        }
        else
        {
            model->SerializeAffected(container->Payloads);
        }

        if (container->Payloads.empty())
        {
            LOG_DEBUG("Model {} has empty payload. {}:{}", model->GetModelName(), location.file_name(), location.line());
            continue;
        }

        if (currentIdx == modelCount)
        {
            container->Callbacks.swap(m_ResultFuncs);
        }

        PostQuery(container);
    }
}

bool NSStorageUpdater::PostQuery(const std::shared_ptr<NSStorageUpdateContainer> container)
{
    if (container->ProcedureName.empty())
    {
        LOG_ERROR("Model #{} has invalid UpsertName.", container->ModelId);
        return false;
    }

    // NSDataBaseManager의 StorageUpdateQuery가 구현될 때까지 임시 처리
    // NSDataBaseManager::GetInstance()->StorageUpdateQuery(container,
    //     std::bind(&NSStorageUpdater::UpdateFunc, std::placeholders::_1, std::placeholders::_2),
    //     std::bind(&NSStorageUpdater::ResultFunc, std::placeholders::_1, std::placeholders::_2),
    //     container->Session);

    // 비동기로 StorageUpdateQuery 실행
    NSDataBaseManager::GetInstance()->StorageUpdateQuery(container,
        std::bind(&NSStorageUpdater::UpdateFunc, std::placeholders::_1, std::placeholders::_2),
        std::bind(&NSStorageUpdater::ResultFunc, std::placeholders::_1, std::placeholders::_2),
        container->Session)
    .Then([modelId = container->ModelId, seq = container->Seq](std::shared_ptr<NSQueryData> queryData) {
        // 성공 처리는 ResultFunc에서 이미 완료됨
        // container 대신 필요한 값만 캡처
    })
    .Catch([session = std::weak_ptr<NSDBSession>(container->Session), modelId = container->ModelId, seq = container->Seq](std::exception_ptr e) {
        try {
            std::rethrow_exception(e);
        } catch (const std::exception& ex) {
            LOG_ERROR("StorageUpdateQuery failed for Model #{} seq {}: {}", 
                modelId, seq, ex.what());
            if (auto strongSession = session.lock()) {
                DisconnectSession(strongSession, EErrorCode::DBError);
            }
        }
    });

    return true;
}

bool NSStorageUpdater::PostQueryDelay(int delayMS, const std::shared_ptr<NSStorageUpdateContainer> container)
{
#ifdef _DEBUG
    LOG_DEBUG("NSStorageUpdater::PostQueryDelay() has started. delayMS: {}", delayMS);
#endif

    // NSDataBaseManager를 통해 안전하게 지연된 작업 실행
    NSDataBaseManager::GetInstance()->PostDelayedTask(delayMS, [container]() {
        PostQuery(container);
    });
    return true;
}

EErrorCode NSStorageUpdater::UpdateFunc(const std::shared_ptr<NSQueryData> queryData, const std::shared_ptr<NSStorageUpdateContainer> container)
{
    // 프로시저 호출 직전에 시퀀스 할당
    int64_t seq = NSDataBaseManager::GetInstance()->GetNextStorageSequence(container->Cid);
    container->Seq = seq;  // container에 시퀀스 설정
    
    // 샤드별 연결 풀 가져오기
    auto* pool = NSDataBaseManager::GetInstance()->GetDBConnection(static_cast<int32>(EDataBase::Game));
    if (!pool)
    {
        LOG_ERROR("failed to get connection pool. Model #{} seq {} procedure {}",
            container->ModelId, seq, container->ProcedureName);
        return EErrorCode::DBConnectionError;
    }

#ifdef _DEBUG
    LOG_DEBUG("CID {} seq {} UpdateFunc started. procedure: {}", container->Cid, seq, container->ProcedureName);
#endif

    // 사용 가능한 연결 가져오기
    auto connection = pool->GetConnection();
    if (!connection)
    {
        LOG_ERROR("failed to connect database. Model #{} seq {} procedure {}",
            container->ModelId, seq, container->ProcedureName);
        return EErrorCode::DBConnectionError;
    }

    // MySQL 명령 생성 및 파라미터 바인딩
    MySQLCommand command(connection.get(), container->ProcedureName);
    
    command.BindParameter("WID", container->Wid);
    command.BindParameter("AID", container->Aid);
    command.BindParameter("CID", container->Cid);
    command.BindParameter("SEQ", seq);  // 방금 할당받은 시퀀스 사용

    if (container->Method == EStorageUpdateMethod::EscrowDeposit)
    {
        command.BindParameter("EscrowTransactionId", container->EscrowTransactionId);
        command.BindParameter("EscrowPayload", container->EscrowPayload);
    }
    else if (container->Method == EStorageUpdateMethod::EscrowWithdraw)
    {
        command.BindParameter("EscrowTransactionId", container->EscrowTransactionId);
    }
    else if (container->Method == EStorageUpdateMethod::EscrowReclaim)
    {
        command.BindParameter("EscrowTransactionId", container->EscrowTransactionId);
    }

    size_t payloadCount = container->Payloads.size();
    LOG_DEBUG("Cid {} Model #{} Seq {} has {} payloads.", container->Cid, container->ModelId, seq, payloadCount);
    for (size_t i = 0; i < payloadCount; ++i)
    {
        if (!container->Payloads.at(i).empty() && container->Payloads.at(i) != "[]")
        {
            LOG_DEBUG("Cid {} Model #{} Seq {} @Payload{}: {}", container->Cid, container->ModelId, seq, i, container->Payloads.at(i).c_str());
        }
        else
        {
            LOG_DEBUG("Cid {} Model #{} Seq {} @Payload{} is empty", container->Cid, container->ModelId, seq, i);
        }

        std::string paramName = "Payload" + std::to_string(i);
        command.BindParameter(paramName, container->Payloads.at(i));
    }

    // 프로시저 실행
    auto recordSet = command.Execute();
    if (!recordSet)
    {
        LOG_ERROR("Cid {} Model #{} Seq {} failed to execute {}.",
            container->Cid, container->ModelId, seq, container->ProcedureName);
        return EErrorCode::DBError;
    }

    // QueryData에 결과 설정
    queryData->SetReturnValue(0); // 성공으로 가정
    queryData->SetErrorCode(EErrorCode::None);

    LOG_DEBUG("Cid {} Model #{} Seq {} has executed [{}]",
        container->Cid, container->ModelId, seq, container->ProcedureName);

    return EErrorCode::None;
}

EErrorCode NSStorageUpdater::ProcessSequence(const std::shared_ptr<NSStorageUpdateContainer> container)
{
    if (container->Session == nullptr || container->Session->IsClosed() || container->Session->IsClosing())
    {
        // relay to another game servers
#ifdef _DEBUG
        LOG_DEBUG("NSStorageUpdater::ProcessSequence() session is not exists. relay the event to other processes...");
#endif
    }

    // 시퀀스 검증은 DB 프로시저에서 수행
    // 여기서는 성공한 것으로 간주
    
    return EErrorCode::None;
}

EErrorCode NSStorageUpdater::ResultFunc(const std::shared_ptr<NSQueryData> queryData, const std::shared_ptr<NSStorageUpdateContainer> container)
{
    int32_t returnValue = queryData->GetReturnValue();
    EErrorCode executeError = queryData->GetErrorCode();
    if (executeError != EErrorCode::None)
    {
        switch (executeError)
        {
        case EErrorCode::DBConnectionError:
        {
            // 재시도 대신 즉시 종료
            DisconnectSession(container->Session, executeError);
        }
        break;
        case EErrorCode::AdoCommandNullptr:
        {
            DisconnectSession(container->Session, executeError);
        }
        break;
        case EErrorCode::DBArgumentError:
        {
            DisconnectSession(container->Session, executeError);
        }
        break;
        case EErrorCode::DBError:
        {
            // retry? error?
            // disconnect test
            DisconnectSession(container->Session, executeError);
        }
        break;
        case EErrorCode::DBSequenceUpdateError:
        {
            DisconnectSession(container->Session, executeError);
        }
        break;
        }

        return executeError;
    }

    if (returnValue != 0)
    {
        LOG_ERROR("Cid {} Model #{} Seq {} found error return. [{}]",
            container->Cid, container->ModelId, container->Seq, returnValue);
        return EErrorCode::DBError;
    }

    ProcessSequence(container);

    if (!container->Callbacks.empty())
    {
        for (auto& callback : container->Callbacks)
        {
            callback(queryData, container.get());
        }
    }

    return EErrorCode::None;
}

void NSStorageUpdater::DisconnectSession(std::shared_ptr<NSDBSession> session, EErrorCode error)
{
    if (session != nullptr && session->GetAID() != 0)
    {
        session->SendSystemNtfThenClose(error);
    }
}

#ifdef _DEBUG
void NSStorageUpdater::SetCommitCalled()
{
    m_CommitCalled = true;
}
#endif