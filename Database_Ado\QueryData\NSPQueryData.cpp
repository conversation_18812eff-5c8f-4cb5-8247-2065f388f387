#include "stdafx.h"
#include "NSPQueryData.h"

#include "ADO/NSAdoCommand.h"
#include "ADO/NSAdoRecordset.h"
#include "QueryData/NSDataSerializer.h"

NSPQueryData::NSPQueryData()
{
	m_pcDataSerializer = new NSDataSerializer();

	for (int i = 0; i < g_uMaxQueryRecordSetCount; i++)
	{
		m_iReturnValue[i] = -100;
		m_pcAdoRecordset[i] = nullptr;
	}

	memset(m_pcAdoCommand, 0, sizeof(m_pcAdoCommand));
	m_pcAdoRecordset[0] = new NSAdoRecordset(); //초기에는 하나만 생성해둔다.
	m_eErrorCode = EErrorCode::None;
	m_iQuerySetCount = 0;
	m_mapRecordsetByName.clear();
	m_mapReturnValueByName.clear();
	m_mapCommandNameByIdx.clear();

	m_pAllocFuncName = nullptr;
	m_iAllocLine = 0;
}

NSPQueryData::~NSPQueryData()
{
	if (m_pcDataSerializer)
		delete m_pcDataSerializer;

	for (int i = 0; i < g_uMaxQueryRecordSetCount; i++)
	{
		if (m_pcAdoRecordset[i] != nullptr)
			delete m_pcAdoRecordset[i];

		if (m_pcAdoCommand[i] != nullptr)
			m_pcAdoCommand[i] = nullptr;
	}
}

void NSPQueryData::Reset()
{
	m_eErrorCode = EErrorCode::None;
	m_iQuerySetCount = 0;
	memset(m_pcAdoCommand, 0, sizeof(m_pcAdoCommand));
	for (int i = 0; i < g_uMaxQueryRecordSetCount; i++)
	{
		m_iReturnValue[i] = -100;
		NSAdoRecordset* pcRs = m_pcAdoRecordset[i];
		if (pcRs != nullptr)
		{
			pcRs->Reset();
			if (i > 0)
			{
				//하나만 두고 모두 삭제한다.
				delete pcRs;
				m_pcAdoRecordset[i] = nullptr;
			}
		}
	}

	m_pcDataSerializer->Clear();
	m_mapRecordsetByName.clear();
	m_mapReturnValueByName.clear();

	m_pAllocFuncName = nullptr;
	m_iAllocLine = 0;
}

NSDataSerializer& NSPQueryData::GetQueryData()
{
	return (*m_pcDataSerializer);
}

NSAdoRecordset* NSPQueryData::GetAdoRecordSet()
{
	return m_pcAdoRecordset[0];
}

NSAdoRecordset* NSPQueryData::GetAdoRecordSet(int iIndex)
{
	if (iIndex < 0 || iIndex >= m_iQuerySetCount)
		return nullptr;

	if (m_pcAdoRecordset[iIndex] == nullptr)
		return nullptr;

	return m_pcAdoRecordset[iIndex];
}

NSAdoRecordset* NSPQueryData::GetAdoRecordSet(const char* strCommandName)
{
	if (strCommandName == nullptr)
		return nullptr;

	MAP_RECORDSET::iterator it = m_mapRecordsetByName.find(strCommandName);
	if (it == m_mapRecordsetByName.end())
		return nullptr;

	return it->second;
}

bool NSPQueryData::SetAdoCommand(NSAdoCommand* pcAdoCommand)
{
	if (pcAdoCommand == nullptr)
		return false;

	if (m_iQuerySetCount >= g_uMaxQueryRecordSetCount)
	{
		LOGE << "AdoRecordset buffer Over!!";
		return false;
	}

	m_pcAdoCommand[m_iQuerySetCount] = pcAdoCommand;
	if (m_pcAdoRecordset[m_iQuerySetCount] == nullptr)
	{
		m_pcAdoRecordset[m_iQuerySetCount] = new NSAdoRecordset();
	}

	++m_iQuerySetCount;
	return true;
}

void NSPQueryData::SetAdoRecordSet(std::string strCommandName, NSAdoRecordset* pcRecordSet)
{
	MAP_RECORDSET::iterator it = m_mapRecordsetByName.find(strCommandName);
	if (it != m_mapRecordsetByName.end())
		return;

	m_mapRecordsetByName.insert(std::make_pair(strCommandName, pcRecordSet));
}

void NSPQueryData::SetReturnValue(int iIndex, std::string strCommandName, int iReturnValue)
{
	if (iIndex < 0 || iIndex >= m_iQuerySetCount)
		return;

	m_iReturnValue[iIndex] = iReturnValue;

	{
		MAP_RETURNVALUE::iterator it = m_mapReturnValueByName.find(strCommandName);
		if (it == m_mapReturnValueByName.end())
		{
			m_mapReturnValueByName.insert(std::make_pair(strCommandName, iReturnValue));
		}
	}

	{
		MAP_COMMANDNAME::iterator it = m_mapCommandNameByIdx.find(iIndex);
		if (it == m_mapCommandNameByIdx.end())
		{
			m_mapCommandNameByIdx.insert(std::make_pair(iIndex, strCommandName));
		}
	}
}

NSAdoCommand* NSPQueryData::GetAdoCommand(int iIndex)
{
	if (iIndex < 0 || iIndex >= m_iQuerySetCount)
		return nullptr;

	return m_pcAdoCommand[iIndex];
}

const char* NSPQueryData::GetCommandName(int iIndex)
{
	MAP_COMMANDNAME::iterator it = m_mapCommandNameByIdx.find(iIndex);
	if (it == m_mapCommandNameByIdx.end())
	{
		return "";
	}

	return it->second.c_str();
}

int NSPQueryData::GetReturnValue()
{
	return m_iReturnValue[0];
}

int NSPQueryData::GetReturnValue(int iIndex)
{
	if (iIndex < 0 || iIndex >= m_iQuerySetCount)
		return -100;

	return m_iReturnValue[iIndex];
}

int NSPQueryData::GetReturnValue(const char* strCommandName)
{
	MAP_RETURNVALUE::iterator it = m_mapReturnValueByName.find(strCommandName);
	if (it == m_mapReturnValueByName.end())
		return -100;

	return it->second;
}

void NSPQueryData::SetErrorCode(const EErrorCode eErrorCode)
{
	m_eErrorCode = eErrorCode;
}

EErrorCode NSPQueryData::GetErrorCode() const
{
	return m_eErrorCode;
}

bool NSPQueryData::IsValid() const
{
	return GetErrorCode() == EErrorCode::None;
}

void NSPQueryData::SetElapsedTime(std::chrono::high_resolution_clock::duration elapsedTime)
{
	m_ElapsedTime = elapsedTime;
}

auto NSPQueryData::GetElapsedTime() const->std::chrono::high_resolution_clock::duration
{
	return m_ElapsedTime;
}

