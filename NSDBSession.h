#pragma once
#include <memory>
#include <atomic>

// 게임 세션 인터페이스 (레거시 호환)
class NSDBSession : public std::enable_shared_from_this<NSDBSession>
{
public:
    NSDBSession() = default;
    virtual ~NSDBSession() = default;

    // 계정 ID
    virtual int64_t GetAID() const = 0;
    
    // 캐릭터 ID
    virtual int64_t GetCID() const = 0;
    
    // 세션 상태
    virtual bool IsClosing() const = 0;
    virtual bool IsClosed() const = 0;
    
    // 에러 발생 시 클라이언트에 알림 후 종료
    virtual void SendSystemNtfThenClose(int errorCode) = 0;
    
    // 샤딩용 키 가져오기
    uint64_t GetShardKey() const { return GetAID(); }
};

