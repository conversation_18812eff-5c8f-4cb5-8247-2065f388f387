#pragma once

#include "NPPacketStruct.h"
#include <stdint.h>
#include <string>

#include "json11.hpp"
#include "NSDefineEnum.h"

class Dungeon;
class NSGameEntity;
class ItemReinforce;

struct NSCharacterData
{
	int64_t AID = 0;
	int64_t CID = 0;
	char Name[g_uMaxCharacterNameUTF8Length] = { 0 };
	int32_t ClassType = 0;
	int32_t Level = 0;
	int64_t Exp = 0;
	int32_t Hp = 0;
	int32_t	CharacterTitleID = 0;
	int32_t Faction = 0;
	uint64_t FactionChangeTime = 0;
	uint8_t FactionGrade = 0;
	int64_t FactionReputation = 0;
	uint8_t Gender = 0;
	int32_t MainWeaponType = 0;
	int64_t GuildId = 0;
	uint64_t GuildUpdateAt = 0;
	int32_t WantedLevel = 0;
	int64_t WantedAccumulatedExp = 0;
	ENpLib_CharacterPVPGradeType PvPGrade = ENpLib_CharacterPVPGradeType::None;
	int64_t PvPGradeResetTime = 0;
	int32_t AwakenClassType = 0;
	bool ShowHelmet = true;
	int32_t SeteraChronoRemnants = 0;
	uint64_t DailyResetAt = 0;
};

struct NSCharacterLogoutData
{
	bool IsLogoutDeadState = false;
	uint64_t ReconnectDeadLimitTime = 0;
	int64_t LogoutMapModuleId = 0;
	FVector LogoutDungeonPos = FVector::ZeroVector;
};

struct NSQuestData
{
	int32_t QuestID = 0;
	uint64_t QuestFailTime = 0;
	uint8_t State = 0;
	int32_t StepID = 0;
	uint64_t StepFailTime = 0;
	int32_t TargetCounts[g_uMaxQuestStepTargetCount] = { 0, };
};

struct NSQuestCompletedData
{
	int32_t QuestId = 0;
	int32_t CompletedCount = 0;
	int32_t TotalCompletedCount = 0;
	uint64_t NextResetTime = 0;
};

struct NSCompletedQuestStepData
{
	int32_t QuestId{ 0 };
	char QuestStepCodes[g_uMaxCompletedQuestStepCount]{};

	void Clear();
	bool AddStepCode(char stepCode);
	bool IsStepCompleted(int32_t questStepId) const;

	static auto MakeStepCode(int32_t questId, int32_t questStepId) -> char;
	static auto GetQuestStepIdFromStepCode(int32_t questId, char stepCode) -> int32_t;
};

struct NSQuestActivate
{
	int32_t QuestId = 0;
	bool IsActivate = false;
};

struct NSRequestQuest
{
	int32_t QuestId = 0;
	ENpLib_RequestType RequestType = ENpLib_RequestType::None;
	ENpLib_QuestState QuestState = ENpLib_QuestState::None;
};

//던전정보
struct NSDungeonInfo
{
	NSDungeonInfo() { Reset(); }
	void Reset() { memset(this, 0, sizeof(*this)); }

	ENpLib_DungeonTryState tryState = ENpLib_DungeonTryState::None;
	bool challengeClosed[g_iChallengeInfoMaxCount];
	const Dungeon* pDungeonTemplate; //던전 정보

};

struct NSDungeonRecord
{
	NSDungeonRecord() { Reset(); }
	void Reset() { memset(this, 0, sizeof(*this)); }

	int32_t totalDungeonEnters = 0;
	int32_t totalEscapesSucceeded = 0;
	int32_t totalPlayerKills = 0;
	int64_t totalMonsterKills = 0;
	int64_t totalInteractions = 0;
	int64_t totalDistanceMoved = 0;
	int64_t averageEscapeTime = 0;
	int32_t maxEscapeTime = 0;
};

struct NSRewardCollectItemInfo
{
	NSRewardCollectItemInfo() { Reset(); }
	void Reset() { memset(this, 0, sizeof(*this)); }

	int32_t itemProbability = 0;
	int32_t itemId = 0;
	int32_t minQuantity = 0;
	int32_t maxQuantity = 0;
};

struct NSAddLifeInfo
{
	NSAddLifeInfo() = default;
	NSAddLifeInfo(NSGameEntity* caster, NSGameEntity* effectiveCaster, NSGameEntity* target, const class NSUnitExec* unitExec, NPUnitChangeStateReason::Reason effectReason, int32_t effectiveExecId, int32_t specialDeadStatusEffect)
		: Caster(caster), EffectiveCaster(effectiveCaster), Target(target), UnitExec(unitExec), EffectReason(effectReason), EffectiveExecId(effectiveExecId), specialDeadStatusEffect(specialDeadStatusEffect)
	{
	}

	NSGameEntity* Caster = nullptr;
	NSGameEntity* EffectiveCaster = nullptr;
	NSGameEntity* Target = nullptr;
	const class NSUnitExec* UnitExec{};
	int32_t EffectiveExecId = -1;
	int32_t specialDeadStatusEffect = -1;
	NPUnitChangeStateReason::Reason EffectReason = NPUnitChangeStateReason::Reason::None;
};

struct NSGroupPatrolBlackboard
{
	NSGroupPatrolBlackboard()
	{
		ActiveSpawn = true;
		ResetBlackboard();
	}
	void ResetBlackboard()
	{
		GroupLeader = false;
		ReverseWay = false;

		GroupPatrolPos = FVector::ZeroVector;
		PrePathPointIndex = 0;
		CurPathPointIndex = 0;
		TotalPathPointIndex = 0;
		TotalNextPathPointIndex = 0;
	}

	bool GroupLeader = false;
	bool ActiveSpawn = true;
	bool ReverseWay = false;

	FVector GroupPatrolPos = FVector::ZeroVector;
	size_t PrePathPointIndex = 0;
	size_t CurPathPointIndex = 0;
	size_t TotalPathPointIndex = 0;
	size_t TotalNextPathPointIndex = 0;
};

struct NSAbilityData
{
	ENpLib_AbilityType	AbilityType = ENpLib_AbilityType::None;
	int32_t				Level = 0;
	int32_t				Exp = 0;
	//[Ramsey] M12 전문화 퀘스트 완료 플래그.
	bool				Specialized = false;
	int64_t				AccumulatedExp = 0;
};



struct NSEmotionString
{
	EEmotionStrType					m_emotionStrType;
	std::string						m_strData;
	bool							m_IsDefault;

	NSEmotionString()
		: m_emotionStrType(EEmotionStrType::None), m_strData(""), m_IsDefault(true)
	{
	}
	NSEmotionString(const EEmotionStrType emotionStrType, const std::string& strData, const bool isDefault)
	{
		m_emotionStrType = emotionStrType;
		m_strData = strData;
		m_IsDefault = isDefault;
	}
};


struct NSEmotionSlot
{
	NSEmotionSlot() = default;
	NSEmotionSlot(const uint8_t idx, const int32_t emotionDataid)
	{
		m_nIndex = idx;
		m_nEmotionDataId = emotionDataid;
	}

	uint8_t		m_nIndex;
	int32_t		m_nEmotionDataId;

	void Reset()
	{
		m_nEmotionDataId = 0;
	}
};

struct NSChronotectorNodeData
{
	int32_t NodeTid = 0;
	int32_t JewelItemID = 0;
	bool isActivate = false;
};


struct NSChronotectorNodeAmplifyJewelData
{
	//jewel item Id
	int32_t JewelItemID = 0;
	//jewel 이 장착된 Node의 Id
	int32_t JewelsBaseNodeTid = 0;
	//jewel에 의해 증폭효과를 받은 Node 들
	std::unordered_set<int32_t> AffectedNodeIds;
};

struct NSStableVehicleInfo
{
	NPStableVehicleInfo info;
};

struct NSCraftUnlockInfo
{
	NPCraftUnlockInfo Info;
	bool Activated = false;
};

struct NSInitContentsInfo
{
	ENpLib_InitContentsType ContentsType = ENpLib_InitContentsType::None;
	int32_t InitCount = 0;
};

struct NSWaitReconnectContext
{
	NSWaitReconnectContext() = default;
	NSWaitReconnectContext(uint64_t sid, int64_t aid, int64_t cid, uint64_t expireTick, int32_t regionId)
		: Sid(sid), Aid(aid), Cid(cid), ExpireTick(expireTick), RegionId(regionId)
	{
	}

	uint64_t Sid = 0;
	int64_t Aid = 0;
	int64_t Cid = 0;
	uint64_t ExpireTick = 0;
	int32_t RegionId = 0;
};

struct NSActiveEntityCount
{
	void Reset()
	{
		MonsterActiveCount = 0;
	}
	int32_t MonsterActiveCount = 0;
};

struct NSTriggerVolumeResult
{
	bool IsEnter = false;
	int32_t RegionId = 0;
	int32_t TriggerVolumeId = 0;
};

struct NSPacketStatisticsInfo
{
	int64_t RecvPacketSize = 0;
	int64_t SendPacketSize = 0;
};

struct NSPacketSaveJsonFormatData
{
	int8_t SaveJsonFormatDataId = 0;
	std::string SaveJsonFormatDataString;
};

struct NSMapTower
{
	int32_t DataId;
	int64_t PropId;
};

struct NSCloudColorCode
{
	int32_t DataId;
	int32_t ColorCode;
};

struct NSMonsterSoulInfo
{
	NPMonsterSoulInfo ContentsData;
	bool Activated{ false };
};

struct NSReinforceData
{
	const ItemReinforce* pcData = nullptr;
	int32_t totalAddGearScore = 0;
};

struct NSReinforceBonusData
{
	bool isHaveReinforceCommonPerk = false;
	int32_t totalAddUniquePerkLevel = 0;
};

struct NSPathFindParam
{
	FVector BeginPos = FVector::ZeroVector;
	FVector DestPos = FVector::ZeroVector;
	float CheckDestDistance = 0.f;
	bool ForceDestPos = false;
	bool IsYaw = false;
	float Yaw = 0.f;
	bool IsMoveHoming = false;
	FVector* OutPutDestPos = nullptr;
};

struct NSBtDebigInfo
{
	uint64_t LastTick = 0;
	int32_t LastTaskType = 0;
};

struct NSDespawnInfo
{
	ENpLib_DimensionDespawn Reason = ENpLib_DimensionDespawn::None;
	std::shared_ptr<NSGameEntity> AffectedEntity = nullptr;
	int32_t WorldEventID = 0;
	int32_t CorruptionID = 0;
	std::string ScriptFileName;

	void Reset()
	{
		Reason = ENpLib_DimensionDespawn::None;
		AffectedEntity.reset();
		WorldEventID = 0;
		CorruptionID = 0;
		ScriptFileName.clear();
	}
};

struct NSCharacterMatrixContentsRecord
{
	int32_t TotalMatrixExpAccquired = 0;
};

struct NSCharacterMatrixContentsCoresRecord
{
	int32_t DataID = 0;
	bool Activated = false;
};

struct NSLevelUpMasterySkill
{
	ENpLib_CharacterWeaponType WeaponType{ ENpLib_CharacterWeaponType::None };
	int32_t MasteryId = 0;
	int32_t LevelUpPoint = 1;
};

struct NSLevelDownMasterySkill
{
	ENpLib_CharacterWeaponType WeaponType{ ENpLib_CharacterWeaponType::None };
	int32_t MasteryId = 0;
	int32_t LevelDownPoint = 1;
};

struct NSConsumeEntry
{
	ENpLib_ConsumeType Type = ENpLib_ConsumeType::None;
	int32_t ID = -1;
	int32_t Value = -1;
};

struct NSMatchCharacter
{
	int32_t RegionId;
	int64_t CId;
	uint64_t MatchStartTime;
};