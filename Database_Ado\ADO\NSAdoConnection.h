#pragma once

#include "NSAdoDefine.h"
#include "NSAdoCommand.h"
#include "NSAdoRecordset.h"

class NSAdoConnection
{
public:
	NSAdoConnection();
	virtual ~NSAdoConnection();

	void dump_com_error(const _com_error& e);

	//접속처리
	bool Connect();
	bool Connect(const char* connectionString);

	//트랜잭션
	void BeginTrans();
	void CommitTrans();
	void RollbackTrans();

	//쿼리 실행
	auto ExecuteQuery(const std::string_view query) -> std::unique_ptr<NSAdoRecordset>;

	auto GetConnection() const ->_ConnectionPtr { return m_Connection; }
	auto GetCommand(const std::string_view commandName)->NSAdoCommand*;
	bool AddCommand(const std::string_view commandName, NSAdoCommand* commandObj);

private:
	std::unordered_map<std::string, NSAdoCommand*> m_Commands;
	_ConnectionPtr m_Connection;
	char m_ConnectionInfo[256];
	bool m_isConnecting = false;
};
