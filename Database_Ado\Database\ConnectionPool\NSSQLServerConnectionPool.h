#pragma once

#include "Database/NSConnectionPool.h"

class NSSQLServerConnectionPool : public NSConnectionPool
{
public:
	NSSQLServerConnectionPool(const std::string_view host, uint32_t port,
		const std::string_view dbName, const std::string_view user, const std::string_view password);
	virtual ~NSSQLServerConnectionPool() = default;

private:
	auto Alloc() -> NSAdoConnection* override;

	DataTypeEnum GetDataTypeEnum(int type);
	ParameterDirectionEnum GetParameterDirectionEnum(int type);
};
