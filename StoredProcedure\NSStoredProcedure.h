#pragma once
#include <memory>
#include <string>

// Forward declarations
class MySQLCommand;
class NSDataSerializer;
class NSQueryData;
class NSMySQLConnection;

// EErrorCode는 게임서버의 NPErrorCode.h에서 제공됨

// 프로시저 유틸 매크로 (레거시 호환)
#define DECLARE_PROCEDURE_UTIL \
    public: \
        static const char* GetProcedureName() { return #NAME; } \
        virtual const char* GetName() const override { return GetProcedureName(); }

// 심플한 저장 프로시저 베이스 클래스
class NSStoredProcedure
{
public:
    NSStoredProcedure() = default;
    virtual ~NSStoredProcedure() = default;

    // 프로시저 이름
    virtual const char* GetName() const = 0;
    
    // DB 타입 반환 (레거시 방식)
    virtual EDataBase GetDBType() const = 0;

    // 입력 파라미터 설정 (게임서버에서 구현)
    virtual EErrorCode MakeQuery(MySQLCommand* command) = 0;
    
    // 출력 파라미터 가져오기 (게임서버에서 구현)
    virtual EErrorCode MakeOutput(MySQLCommand* command) = 0;

    // 프로시저 실행 (라이브러리에서 처리)
    std::shared_ptr<NSQueryData> Execute(NSMySQLConnection* connection, 
                                         const NSDataSerializer& serializer);

protected:
    // 에러 처리
    void SetError(int errorCode, const std::string& errorMsg);
    
    // 로깅
    void LogError(const std::string& msg);
    
    // 에러 정보
    int m_errorCode = 0;
    std::string m_errorMessage;
};