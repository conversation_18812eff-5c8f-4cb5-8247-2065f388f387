#include "stdafx.h"
#include "NSAdoRecordset.h"

#include "NPPacketStruct.h"
#include "NSAdoConnection.h"

NSAdoRecordset::NSAdoRecordset()
	: m_pRecordset(nullptr)
	, m_bFirstRecord(true)
{
	m_szCommandName.clear();
}

NSAdoRecordset::~NSAdoRecordset()
{
	Reset();
}

void NSAdoRecordset::dump_com_error(const _com_error& e, const char* pCommadText, const char* pFieldName)
{
	_bstr_t bstrSource(e.Source());
	_bstr_t bstrDescription(e.Description());
	LOGE << std::format("Recordset Error Code = {:#08x}, Code meaning = {}, Source = {}, Description = {}, command={}, FieldName={}, sp={}",
		e.Error(), (LPCSTR)e.ErrorMessage(), (LPCSTR)bstrSource, (LPCSTR)bstrDescription, pCommadText, pFieldName, m_szCommandName.c_str());
}

bool NSAdoRecordset::Open(NSAdoConnection* pcConnection, const char* query)
{
	if (!pcConnection)
		return false;

	if (m_pRecordset)
	{
		Close();
	}
	m_pRecordset.CreateInstance(__uuidof(Recordset));
	m_pRecordset->CursorLocation = adUseClient;

	try
	{
		m_pRecordset->Open(query, pcConnection->GetConnection().GetInterfacePtr(), adOpenForwardOnly, adLockBatchOptimistic, -1);
		LoadColumn(m_pRecordset);
		m_pRecordset->PutRefActiveConnection(nullptr);
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", query);
		return false;
	}

	return true;
}

bool NSAdoRecordset::IsEOF()
{
	if (m_pRecordset == nullptr)
		return true;

	if (m_pRecordset->GetState() != adStateOpen)
		return true;

	if (m_bFirstRecord == false)
	{
		try
		{
			m_pRecordset->MoveNext();
		}
		catch (_com_error& e)
		{
			dump_com_error(e, "MoveNext", "");
			return true;
		}
	}
	else
	{
		m_bFirstRecord = false;
	}

	try
	{
		if (!m_pRecordset->adoEOF)
			return false;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "IsEOF", "");
		return true;
	}

	return true;
}

void NSAdoRecordset::Close()
{
	m_mapColumnIndex.clear();
	try
	{
		if (m_pRecordset)
		{
			if (m_pRecordset->GetState() == adStateOpen)
				m_pRecordset->Close();
			m_pRecordset.Release();
			m_pRecordset = nullptr;
		}
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", __FUNCTION__);
	}

	m_bFirstRecord = true;
}

void NSAdoRecordset::Reset()
{
	if (m_pRecordset != nullptr)
		Close();

	m_bFirstRecord = true;
	m_szCommandName.clear();
}

bool NSAdoRecordset::GetItem(const char* pFieldName, int& iValue)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adInteger);
		if (pcField == nullptr)
			return false;
		iValue = pcField->GetValue().intVal;
		//iValue = m_pRecordset->Fields->GetItem(pFieldName)->GetValue().intVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, bool& bValue)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adBoolean);
		if (pcField == nullptr)
			return false;
		bValue = (DWORD)pcField->GetValue().bVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, uint8_t& uValue)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adUnsignedTinyInt);
		if (pcField == nullptr)
			return false;
		uValue = (DWORD)pcField->GetValue().bVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, uint16_t& uValue)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adUnsignedSmallInt);
		if (pcField == nullptr)
			return false;
		uValue = (USHORT)pcField->GetValue().uiVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, uint32_t& uValue)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adUnsignedInt);
		if (pcField == nullptr)
			return false;
		uValue = (DWORD)pcField->GetValue().uintVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, int64_t& iValue)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adBigInt);
		if (pcField == nullptr)
			return false;
		const _variant_t varValue = pcField->GetValue();

		iValue = varValue.llVal;
		if (varValue.decVal.sign == DECIMAL_SIGN_NEGATIVE)
		{
			iValue *= -1;
		}
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, uint64_t& uValue)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adUnsignedBigInt);
		if (pcField == nullptr)
			return false;
		uValue = pcField->GetValue().ullVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, int16_t& iValue)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adSmallInt);
		if (pcField == nullptr)
			return false;
		iValue = pcField->GetValue().uiVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, float& fValue)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adDouble);
		if (pcField == nullptr)
			return false;
		fValue = (float)pcField->GetValue().dblVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, char& value)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adTinyInt);
		if (pcField == nullptr)
			return false;
		value = pcField->GetValue().bVal;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, char* pValue, int iSize)
{
	try
	{
		ADODB::FieldPtr pcField = GetColumn(pFieldName, adVarChar);
		if (pcField == nullptr)
			return false;
		switch (m_pRecordset->Fields->GetItem(pFieldName)->GetType())
		{
		case adChar:
		case adVarChar:
		case adLongVarChar:
		{
			if (pcField->GetValue().bstrVal == nullptr)
				return false;

			strncpy_s(pValue, iSize, (char*)(_bstr_t(pcField->GetValue().bstrVal)), _TRUNCATE);
		}
		break;
		case adWChar:
		case adVarWChar:
		case adLongVarWChar:
		{
			if (pcField->GetValue().bstrVal == nullptr)
				return false;

			wcsncpy_s(m_unicode, (LPWSTR)(_bstr_t(pcField->GetValue().bstrVal)), iSize);

			WideCharToMultiByte(CP_UTF8, 0, m_unicode, -1, pValue, iSize, nullptr, nullptr);
		}
		break;
		}
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* fieldName, std::string& value)
{
	try
	{
		ADODB::FieldPtr field = GetColumn(fieldName, adVarChar);
		if (field == nullptr)
			return false;

		switch (m_pRecordset->Fields->GetItem(fieldName)->GetType())
		{
		case adChar:
		case adVarChar:
		case adLongVarChar:
		{
			if (field->GetValue().bstrVal == nullptr)
				return false;

			_bstr_t bstr(field->GetValue().bstrVal);
			std::string stdString((const char*)bstr, bstr.length());
		}
		break;
		case adWChar:
		case adVarWChar:
		case adLongVarWChar:
		{
			if (field->GetValue().bstrVal == nullptr)
				return false;

			_bstr_t bstr(field->GetValue().bstrVal);

			int requiredSize = ::WideCharToMultiByte(CP_UTF8, 0, (const wchar_t*)bstr, -1, nullptr, 0, nullptr, nullptr);
			value.resize(requiredSize);

			::WideCharToMultiByte(CP_UTF8, 0, (const wchar_t*)bstr, -1, value.data(), requiredSize, nullptr, nullptr);
		}
		break;
		}
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", fieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, uint32_t uIndex, uint16_t& uValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return GetItem(strFieldName, uValue);
}

bool NSAdoRecordset::GetItem(const char* pFieldName, uint32_t uIndex, int& iValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return GetItem(strFieldName, iValue);
}

bool NSAdoRecordset::GetItem(const char* pFieldName, uint32_t uIndex, uint32_t& uValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return GetItem(strFieldName, uValue);
}

bool NSAdoRecordset::GetItem(const char* pFieldName, uint32_t uIndex, uint64_t& uValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return GetItem(strFieldName, uValue);
}

bool NSAdoRecordset::GetItem(const char* pFieldName, uint32_t uIndex, uint8_t& uValue)
{
	char strFieldName[128] = {
		0,
	};
	sprintf_s(strFieldName, sizeof(strFieldName), "%s%u", pFieldName, uIndex);

	return GetItem(strFieldName, uValue);
}
void NSAdoRecordset::SetRecordset(_RecordsetPtr pRecordSet)
{
	if (pRecordSet == nullptr)
		return;

	try
	{
		m_pRecordset = pRecordSet->Clone(adLockReadOnly);
		m_pRecordset->putref_DataSource(nullptr);
		LoadColumn(m_pRecordset);
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", "");
		m_pRecordset = nullptr;
		return;
	}
}

unsigned long NSAdoRecordset::GetStringHash(const char* pcStr)
{
	unsigned long c, i, h;
	unsigned long uiSize = (unsigned long)strlen(pcStr);
	for (i = h = 0; i < uiSize; ++i)
	{
		c = toupper(pcStr[i]);
		h = ((h << 5) + h) ^ c;
	}
	return h;
}

void NSAdoRecordset::LoadColumn(_RecordsetPtr pRecordset)
{
	if (pRecordset == nullptr)
		return;

	if (pRecordset->GetState() != adStateOpen)
		return;

	if (pRecordset->adoBOF && pRecordset->adoEOF)
		return;

	int iColumnCount = pRecordset->Fields->Count;

	char	   cColumnBuffer[256] = { 0 };
	_variant_t index = 2;
	index.vt = VT_I2;

	for (short i = 0; i < iColumnCount; ++i)
	{
		index.iVal = i;
		strncpy_s(cColumnBuffer, sizeof(cColumnBuffer), (char*)(_bstr_t(pRecordset->Fields->GetItem(&index)->GetName())), _TRUNCATE);
		long iHash = GetStringHash(cColumnBuffer);
		m_mapColumnIndex.insert(std::pair<long, int>(iHash, i));
	}
}

int NSAdoRecordset::GetColumnIndex(const char* pColumnName)
{
	long iHash = GetStringHash(pColumnName);
	auto itrFind = m_mapColumnIndex.find(iHash);
	if (itrFind == m_mapColumnIndex.end())
		return -1;

	return itrFind->second;
}

ADODB::FieldPtr NSAdoRecordset::GetColumn(const char* pColumnName, const DataTypeEnum eVarType)
{
	int		   iIndex = GetColumnIndex(pColumnName);
	_variant_t vindex;
	vindex.vt = VT_I2;
	vindex.iVal = (short)iIndex;
	try
	{
#ifndef _DEBUG
		eVarType;
		return m_pRecordset->Fields->GetItem(&vindex);
#else
		FieldPtr pColumn = m_pRecordset->Fields->GetItem(&vindex);
		const DataTypeEnum eColType = pColumn->GetType();
		bool bValidType = false;
		switch (eVarType)
		{
		case adEmpty:
			LOGE << std::format("adEmpty type is not allowed. Column: [{}], sp: {}", pColumnName, m_szCommandName);
			break;
		case adBigInt:
		case adUnsignedBigInt:
			if (eColType == adBoolean)
			{
				LOGD << std::format("Assigned BIT column on the other type. Column: [{}], sp: {}", pColumnName, m_szCommandName);
				bValidType = true;
				break;
			}
			bValidType = (eColType == adBigInt) || (eColType == adUnsignedBigInt) || (eColType == adNumeric);
			if (bValidType) break;
			[[fallthrough]];
		case adInteger:
		case adUnsignedInt:
			if (eColType == adBoolean || eColType == adBigInt)
			{
				LOGD << std::format("Assigned BIT column on the other type. Column: [{}], sp: {}", pColumnName, m_szCommandName);
				bValidType = true;
				break;
			}
			bValidType = (eColType == adInteger) || (eColType == adUnsignedInt);
			if (bValidType) break;
			[[fallthrough]];
		case adSmallInt:
		case adUnsignedSmallInt:
			if (eColType == adBoolean)
			{
				LOGD << std::format("Assigned BIT column on the other type. Column: [{}], sp: {}", pColumnName, m_szCommandName);
				bValidType = true;
				break;
			}
			bValidType = (eColType == adSmallInt) || (eColType == adUnsignedSmallInt);
			if (bValidType) break;
			[[fallthrough]];
		case adTinyInt:
		case adUnsignedTinyInt:
			if (eColType == adBoolean)
			{
				LOGD << std::format("Assigned BIT column on the other type. Column: [{}], sp: {}", pColumnName, m_szCommandName);
				bValidType = true;
				break;
			}
			bValidType = (eColType == adTinyInt) || (eColType == adUnsignedTinyInt);
			break;
			// end of validate section

		case adDouble:
		case adSingle:
			bValidType = (eColType == adDouble) || (eColType == adSingle);
			break;
			// end of validate section

		case adBoolean:
			bValidType = (eColType == adBoolean) || (eColType == adTinyInt) || (eColType == adUnsignedTinyInt);
			break;
			// end of validate section

		case adDBTimeStamp:
			bValidType = (eColType == adDBTimeStamp);
			break;
			// end of validate section

		case adBinary:
		case adVarBinary:
		case adChar:
		case adVarChar:
		case adLongVarChar:
		case adLongVarBinary:
		case adWChar:
		case adVarWChar:
		case adLongVarWChar:
			bValidType = (eColType == adBinary) || (eColType == adVarBinary) || (eColType == adChar) ||
				(eColType == adVarChar) || (eColType == adLongVarChar) || (eColType == adLongVarBinary) ||
				(eColType == adWChar) || (eColType == adVarWChar) || (eColType == adLongVarWChar);
			break;
			// end of validate section

		default:
			LOGW << std::format("Need to implement a new verification for Type {}. Column: [{}], sp: {}",
				static_cast<typename std::underlying_type_t<DataTypeEnum>>(eVarType), pColumnName, m_szCommandName);
			bValidType = true;
			break;
		}

		if (!bValidType)
		{
			LOGE << std::format("Assigned the column with a wrong type. Column: [{}], sp: {}", pColumnName, m_szCommandName);
		}

		return bValidType ? pColumn : nullptr; // m_pRecordset->Fields->GetItem(&vindex);
#endif
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pColumnName);
		return nullptr;
	}
}

bool NSAdoRecordset::GetItemBinary(char* pFieldName, void* pValue, int& iSize)
{
	try
	{
		FieldPtr p_field = m_pRecordset->Fields->GetItem(pFieldName);

		_variant_t vt = p_field->GetValue();

		unsigned char* pData = nullptr;

		SafeArrayAccessData(vt.parray, (void**)&pData);

		iSize = (int)p_field->ActualSize;
		memcpy(pValue, pData, iSize);

		SafeArrayUnaccessData(vt.parray);
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}

bool NSAdoRecordset::GetItem(const char* pFieldName, NPDateTime& dtTime)
{
	try
	{
		SYSTEMTIME sysTime;

		ADODB::FieldPtr p_field = GetColumn(pFieldName, adDBTimeStamp);
		if (p_field == nullptr)
			return false;

		VariantTimeToSystemTime(p_field->GetValue().date, &sysTime);

		dtTime.year = sysTime.wYear;
		dtTime.month = sysTime.wMonth;
		dtTime.day = sysTime.wDay;
		dtTime.hour = sysTime.wHour;
		dtTime.minute = sysTime.wMinute;
		dtTime.second = sysTime.wSecond;
	}
	catch (_com_error& e)
	{
		dump_com_error(e, "", pFieldName);
		return false;
	}

	return true;
}
