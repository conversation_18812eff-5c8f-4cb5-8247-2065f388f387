#include "stdafx.h"
#include "CIDQueueManager.h"
#include "../NSDataBaseManager.h"

namespace Database
{

std::shared_ptr<CIDQueueManager::CIDQueue> CIDQueueManager::GetOrCreateQueue(int64_t cid)
{
    // Read lock first
    {
        std::shared_lock<std::shared_mutex> readLock(m_cidQueuesMutex);
        auto it = m_cidQueues.find(cid);
        if (it != m_cidQueues.end())
        {
            it->second->UpdateAccessTime();
            return it->second;
        }
    }
    
    // Write lock for creation
    {
        std::unique_lock<std::shared_mutex> writeLock(m_cidQueuesMutex);
        // Double check
        auto it = m_cidQueues.find(cid);
        if (it != m_cidQueues.end())
        {
            it->second->UpdateAccessTime();
            return it->second;
        }
        
        // Create new queue
        auto cidQueue = std::make_shared<CIDQueue>();
        cidQueue->UpdateAccessTime();
        m_cidQueues[cid] = cidQueue;
        return cidQueue;
    }
}

bool CIDQueueManager::EnqueueTask(int64_t cid, QueryTask task)
{
    auto cidQueue = GetOrCreateQueue(cid);
    
    std::lock_guard<std::mutex> lock(cidQueue->mutex);
    cidQueue->tasks.push(std::move(task));
    
    return cidQueue->tasks.size() == 1;  // 첫 번째 작업인지 반환
}

bool CIDQueueManager::DequeueTask(int64_t cid, QueryTask& task)
{
    std::shared_lock<std::shared_mutex> readLock(m_cidQueuesMutex);
    auto it = m_cidQueues.find(cid);
    if (it == m_cidQueues.end())
    {
        return false;
    }
    
    auto cidQueue = it->second;
    std::lock_guard<std::mutex> lock(cidQueue->mutex);
    
    if (cidQueue->tasks.empty())
    {
        return false;
    }
    
    task = std::move(cidQueue->tasks.front());
    cidQueue->tasks.pop();
    return true;
}

bool CIDQueueManager::HasPendingTasks(int64_t cid) const
{
    std::shared_ptr<CIDQueue> queue;
    {
        std::shared_lock<std::shared_mutex> readLock(m_cidQueuesMutex);
        auto it = m_cidQueues.find(cid);
        if (it == m_cidQueues.end())
        {
            return false;
        }
        queue = it->second;
    }
    
    // 맵 락을 해제한 후 큐 락 획득
    std::lock_guard<std::mutex> lock(queue->mutex);
    return !queue->tasks.empty();
}

void CIDQueueManager::ClearCIDTasks(int64_t cid)
{
    std::unique_lock<std::shared_mutex> writeLock(m_cidQueuesMutex);
    m_cidQueues.erase(cid);
}

size_t CIDQueueManager::GetTotalQueueSize() const
{
    size_t totalSize = 0;
    mi::vector<std::pair<int64_t, std::shared_ptr<CIDQueue>>> queueSnapshot;
    
    // 먼저 큐 스냅샷을 만들어 락 시간 최소화
    {
        std::shared_lock<std::shared_mutex> readLock(m_cidQueuesMutex);
        queueSnapshot.reserve(m_cidQueues.size());
        for (const auto& [cid, cidQueue] : m_cidQueues)
        {
            queueSnapshot.emplace_back(cid, cidQueue);
        }
    }
    
    // 이제 개별 큐 락을 안전하게 획득
    for (const auto& [cid, cidQueue] : queueSnapshot)
    {
        if (cidQueue)
        {
            std::lock_guard<std::mutex> lock(cidQueue->mutex);
            totalSize += cidQueue->tasks.size();
        }
    }
    
    return totalSize;
}

} // namespace Database