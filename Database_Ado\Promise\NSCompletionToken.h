#pragma once
#include <cassert>
#include <memory>
#include <mutex>
#include <new>
#include <chrono>
#include <functional>

namespace Detail
{
	class CompletionTokenBase
	{
	public:
		~CompletionTokenBase();

		bool IsDone() const;

		void Wait();
		void WaitFor(const std::chrono::milliseconds& milliseconds);

		void AddCompletionCallback(const std::function<void()>& continuation);

	protected:
		bool Unsafe_IsDone() const;

	protected:
		mutable std::mutex m_Mutex;
		std::condition_variable m_CondVar;
		bool m_Done = false;
		std::vector<std::function<void()>> m_CompletionCallbacks;
	};

	template <typename T>
	class CompletionToken : public CompletionTokenBase
	{
	public:
		auto Get() -> decltype(auto)
		{
			std::unique_lock lock(m_Mutex);
			if (!m_Done)
			{
				m_CondVar.wait(lock, [this]()
					{
						return Unsafe_IsDone();
					});
			}

			return std::move(m_Value.value());
		}

		void Set(T&& value)
		{
			std::vector<std::function<void()>> callbacks;
			{
				std::lock_guard lock(m_Mutex);
				std::swap(m_CompletionCallbacks, callbacks);

				assert(!m_Done);

				m_Value.emplace(std::move(value));
				m_Done = true;
			}

			m_CondVar.notify_one();

			if (!callbacks.empty())
			{
				for (const std::function<void()>& callback : callbacks)
				{
					callback();
				}
			}
		}

		void Set(const T& value)
		{
			std::vector<std::function<void()>> callbacks;
			{
				std::lock_guard lock(m_Mutex);
				std::swap(m_CompletionCallbacks, callbacks);

				assert(!m_Done);

				m_Value.emplace(value);
				m_Done = true;
			}

			m_CondVar.notify_one();

			if (!callbacks.empty())
			{
				for (const std::function<void()>& callback : callbacks)
				{
					callback();
				}
			}
		}

	private:
		std::optional<T> m_Value = std::nullopt;
	};

	template <>
	class CompletionToken<void> : public CompletionTokenBase
	{
	public:
		void Get()
		{
			std::unique_lock lock(m_Mutex);
			if (!m_Done)
			{
				m_CondVar.wait(lock, [this]()
					{
						return Unsafe_IsDone();
					});
			}
		}

		void Set()
		{
			std::vector<std::function<void()>> callbacks;
			{
				std::lock_guard lock(m_Mutex);
				std::swap(m_CompletionCallbacks, callbacks);

				assert(!m_Done);
				m_Done = true;
			}

			m_CondVar.notify_one();

			if (!callbacks.empty())
			{
				for (const std::function<void()>& callback : callbacks)
				{
					callback();
				}
			}
		}
	};
}

struct NullConstructToken {};

template <typename T>
class NSCompletionToken
{
public:
	NSCompletionToken() = default;
	explicit NSCompletionToken(NullConstructToken);

	bool IsValid() const;
	bool IsDone() const;

	decltype(auto) Get();
	void Set(T&& value);
	void Set(const T& value);

	void Wait();
	void WaitFor(const std::chrono::milliseconds& milliseconds);

	void AddCompletionCallback(const std::function<void()>& callback);

private:
	std::shared_ptr<Detail::CompletionToken<T>> m_Impl = std::make_shared<Detail::CompletionToken<T>>();
};

template <>
class NSCompletionToken<void>
{
public:
	NSCompletionToken() = default;
	explicit NSCompletionToken(NullConstructToken);

	bool IsValid() const;
	bool IsDone() const;

	void Get();
	void Set();

	void Wait();
	void WaitFor(const std::chrono::milliseconds& milliseconds);

	void AddCompletionCallback(const std::function<void()>& callback);

private:
	std::shared_ptr<Detail::CompletionToken<void>> m_Impl = std::make_shared<Detail::CompletionToken<void>>();
};

template <typename T>
NSCompletionToken<T>::NSCompletionToken(NullConstructToken)
	: m_Impl(nullptr)
{
}

template <typename T>
bool NSCompletionToken<T>::IsValid() const
{
	return m_Impl.operator bool();
}

template <typename T>
bool NSCompletionToken<T>::IsDone() const
{
	return m_Impl->IsDone();
}

template <typename T>
decltype(auto) NSCompletionToken<T>::Get()
{
	return m_Impl->Get();
}

template <typename T>
void NSCompletionToken<T>::Set(T&& value)
{
	m_Impl->Set(std::move(value));
}

template <typename T>
void NSCompletionToken<T>::Set(const T& value)
{
	m_Impl->Set(value);
}

template <typename T>
void NSCompletionToken<T>::Wait()
{
	m_Impl->Wait();
}

template <typename T>
void NSCompletionToken<T>::WaitFor(const std::chrono::milliseconds& milliseconds)
{
	m_Impl->WaitFor(milliseconds);
}

template <typename T>
void NSCompletionToken<T>::AddCompletionCallback(const std::function<void()>& callback)
{
	m_Impl->AddCompletionCallback(callback);
}