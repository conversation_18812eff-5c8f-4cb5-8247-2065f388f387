#pragma once

#include <DataBase/Query/NSQuery.h>

class NSQrGetDBTime : public NSSmallQuery
{
public:
	NSQrGetDBTime(std::string_view query);
	virtual ~NSQrGetDBTime() = default;

public:
	static std::string_view GetQuery(EDBProvider provider)
	{
		if (provider == EDBProvider::MSOLEDBSQL)
		{
			return	R"(
				SELECT
				DATEDIFF_BIG(millisecond, {d '1970-01-01'}, SYSUTCDATETIME()) AS UnixTime,
				DATEPART (tzoffset, SYSDATETIMEOFFSET()) AS Offset
				)";
		}
		else if (provider == EDBProvider::MSDASQL)
		{
			return R"(
				SELECT
				  UNIX_TIMESTAMP() * 1000 AS UnixTime,
				 TIMESTAMPDIFF(MINUTE, UTC_TIMESTAMP(), NOW()) AS Offset
				)";
		}

		return "";
	}
};
